<?xml version="1.0" encoding="UTF-8"?>
<fo:root xmlns:fo="http://www.w3.org/1999/XSL/Format"
         xmlns:xlink="http://www.w3.org/1999/xlink">
    <fo:layout-master-set>
        <fo:simple-page-master master-name="invoice">
            <fo:region-body margin="18mm"/>
        </fo:simple-page-master>
    </fo:layout-master-set>


    <!-- Page 1 -->
    <fo:page-sequence master-reference="invoice">
        <fo:flow flow-name="xsl-region-body">
            <fo:block-container width="100%" height="100%" margin-top="-0.5cm"  padding="6mm">

                <!-- Watermark container -->
                <fo:block-container absolute-position="absolute" top="-40%" left="-14%" width="0%" height="0%">
                    <fo:block text-align="center" color="white" font-size="48pt" font-family="Arial">
                        <fo:instream-foreign-object content-width="280%" content-height="280%">
                            <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="440%" height="440%" viewBox="0 0 100 100">
                                <image x="0" y="0" width="100%" height="100%" xlink:href="https://s3.ap-southeast-1.wasabisys.com/wexl-strapi-images/doon%20bharati%20report%20card/Nursery%20to%20UKG.jpg"/>
                            </svg>
                        </fo:instream-foreign-object>
                    </fo:block>
                </fo:block-container>
                <fo:table border="none" width="100%" >
                    <fo:table-column column-width="22mm"/>
                    <fo:table-column column-width="150mm"/>
                    <fo:table-body>
                        <fo:table-row>
                            <!-- Logo Cell -->
                            <fo:table-cell display-align="center"> <!-- vertical align logo -->
                                <fo:block text-align="left" padding-top="-10mm">
                                    <fo:external-graphic
                                            src='url("https://s3.ap-southeast-1.wasabisys.com/wexl-strapi-images/doon%20bharati%20report%20card/doonlogo1.png")'
                                            content-width="70px"
                                            content-height="auto"
                                            scaling="uniform"/>
                                </fo:block>
                            </fo:table-cell>

                            <!-- Text Cell -->
                            <fo:table-cell display-align="center">
                                <fo:block text-align="center" padding-top="-22mm" color="#002060">
                                    <fo:instream-foreign-object content-width="100%" content-height="40mm">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%" overflow="visible">
                                            <text x="50%" y="50%"
                                                  text-anchor="middle" dominant-baseline="middle"
                                                  font-size="24pt" font-family="Montserrat, Arial, Helvetica, sans-serif"  font-weight="bold"
                                                  transform="scale(1,1.7)" fill="#15113d">
                                                DOON BHARTI PUBLIC SCHOOL
                                            </text>
                                            <text x="40%" y="105%"
                                                  text-anchor="middle" dominant-baseline="middle" font-weight="bold"
                                                  font-size="18pt" font-family="Montserrat, Arial, Helvetica, sans-serif" fill="#15113d">
                                                SEHATPUR
                                            </text>
                                        </svg>
                                    </fo:instream-foreign-object>
                                </fo:block>
                            </fo:table-cell>
                        </fo:table-row>
                    </fo:table-body>
                </fo:table>
                <fo:block text-align="center" font-size="25pt" font-family="Times New Roman, serif"  font-weight="bold" padding-top="-20mm" color="#15113d">
                    LEARNING VOYAGE
                </fo:block>
                <fo:block  font-weight="bold" text-align="center"
                           font-size="10pt" padding-top="-2mm" >
                    Continuous &amp; Comprehensive Evaluation (CCE)
                </fo:block>
                <fo:block font-weight="bold" text-align="center" font-size="10pt" font-family="Montserrat, Arial, Helvetica, sans-serif" padding-top="2mm">
                    <fo:inline >&#8226; Lauchpad Learning </fo:inline>
                    <fo:inline >&#8226; Mid Mastery Triumph </fo:inline>
                    <fo:inline >&#8226; Advancing Anchors </fo:inline>
                    <fo:inline >&#8226; Yearly Knowledge Voyage</fo:inline>
                </fo:block>

                <fo:block text-align="center" font-size="13pt" font-weight="bold" color="#15113d"
                          space-after="20mm" space-before="8mm">

                    <fo:table padding-left="-6mm" table-layout="fixed" width="100%">
                        <fo:table-column column-width="29%"/>
                        <fo:table-column column-width="43%"/>
                        <fo:table-column column-width="28%"/>

                        <fo:table-body>
                            <fo:table-row>
                                <!-- Session -->
                                <fo:table-cell>
                                    <fo:block>
                                        Session 20
                                        <fo:inline border-bottom="0.6pt solid black" padding-left="2pt" padding-right="2pt"
                                                   th:text="${model.body.sessionStart != null and model.body.sessionStart != '' ? model.body.sessionStart : ' '}">&#160;</fo:inline>
                                        - 20
                                        <fo:inline border-bottom="0.6pt solid black" padding-left="2pt" padding-right="2pt"
                                                   th:text="${model.body.sessionEnd != null and model.body.sessionEnd != '' ? model.body.sessionEnd : ' '}">&#160;</fo:inline>
                                    </fo:block>
                                </fo:table-cell>

                                <!-- Class -->
                                <fo:table-cell>
                                    <fo:block>
                                        Class
                                        <fo:inline border-bottom="0.6pt solid black"
                                                   th:text="${model.body.studentClass != null and model.body.studentClass != '' ? model.body.studentClass : ' '}">&#160;</fo:inline>
                                    </fo:block>
                                </fo:table-cell>

                                <!-- Section -->
                                <fo:table-cell>
                                    <fo:block>
                                        Section
                                        <fo:inline border-bottom="0.6pt solid black"
                                                   th:text="${model.body.section != null and model.body.section != '' ? model.body.section : ' '}">&#160;</fo:inline>
                                    </fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>
                </fo:block>

                <fo:block-container space-before="5mm" border="2.5pt solid #15113d" padding="5mm" margin-top="5mm" margin-left="10mm" padding-right="-10mm">
                    <fo:block font-size="11pt" space-after="5mm" margin-left="-17mm" font-weight="bold">
                        Name :
                        <fo:inline-container width="182pt">
                            <fo:block font-weight="normal" border-bottom="0.6pt dotted black" padding-right="182pt" th:text="${model.body.name}">&#160;</fo:block>
                        </fo:inline-container>
                    </fo:block>

                    <fo:block font-size="11pt" space-after="5mm" margin-left="-17mm" font-weight="bold">
                        Date of Birth :
                        <fo:inline-container width="65pt">
                            <fo:block font-weight="normal" border-bottom="0.6pt dotted black"
                                      th:text="${model.body.dob}">&#160;</fo:block>
                        </fo:inline-container>
                        Admission No.
                        <fo:inline-container width="79pt">
                            <fo:block font-weight="normal" border-bottom="0.6pt dotted black"
                                      th:text="${model.body.admissionNumber}">&#160;</fo:block>
                        </fo:inline-container>
                        Roll No.
                        <fo:inline-container width="53pt">
                            <fo:block font-weight="normal" border-bottom="0.6pt dotted black"
                                      th:text="${model.body.rollNo}">&#160;</fo:block>
                        </fo:inline-container>
                    </fo:block>

                    <fo:block font-size="11pt" space-after="5mm" margin-left="-17mm" font-weight="bold">
                        Father / Guardian’s Name :
                        <fo:inline-container width="260pt">
                            <fo:block font-weight="normal" border-bottom="0.6pt dotted black"
                                      th:text="${model.body.fatherName}"> </fo:block>
                        </fo:inline-container>
                    </fo:block>

                    <fo:block font-size="11pt" space-after="5mm" margin-left="-17mm" font-weight="bold">
                        Residence Address :
                        <fo:inline-container width="290pt">
                            <fo:block font-weight="normal" border-bottom="0.6pt dotted black"
                                      th:text="${model.body.address}"> </fo:block>
                        </fo:inline-container>
                    </fo:block>

                    <fo:block font-size="11pt" space-after="5mm" margin-left="-17mm" font-weight="normal">
                        <fo:inline-container width="401pt">
                            <fo:block border-bottom="0.6pt dotted black">&#160;</fo:block>
                        </fo:inline-container>
                    </fo:block>

                    <fo:block font-size="11pt" space-after="5mm" margin-left="-17mm" font-weight="bold">
                        Telephone Number :
                        <fo:inline-container width="290pt">
                            <fo:block font-weight="normal" border-bottom="0.6pt dotted black"
                                      th:text="${model.body.phoneNumber}">&#160;</fo:block>
                        </fo:inline-container>
                    </fo:block>
                </fo:block-container>

                <fo:block font-size="14pt" font-weight="bold" padding-top="285pt" margin-left="300pt" color="#15113d"> Class :
                    <fo:inline th:text="${model.body.studentClass}"> </fo:inline>
                </fo:block>

            </fo:block-container>
        </fo:flow>
    </fo:page-sequence>

    <!-- 2nd Page -->
    <fo:page-sequence master-reference="invoice">
        <fo:flow flow-name="xsl-region-body">
            <fo:block-container width="100%" height="100%" margin="0mm">

                <!-- Background Image -->
                <fo:block-container absolute-position="absolute" top="-40%" left="114%" width="0%" height="0%">
                    <fo:block text-align="center" color="white" font-size="48pt" font-family="Arial">
                        <fo:instream-foreign-object content-width="280%" content-height="280%">
                            <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="440%" height="440%" viewBox="0 0 100 100">
                                <image x="0" y="0" width="100%" height="100%" xlink:href="https://s3.ap-southeast-1.wasabisys.com/wexl-strapi-images/doon%20bharati%20report%20card/background%20nur-ukg.png"/>
                            </svg> </fo:instream-foreign-object>
                    </fo:block>
                </fo:block-container>

                <fo:block padding-top="-5mm" margin-top="2mm" text-align="center" font-size="14pt" font-weight="bold" color="#15113d">
                    Session 20<fo:inline border-bottom="0.6pt solid black" padding-left="6pt" padding-right="6pt"
                                         th:text="${model.body.sessionStart != null and model.body.sessionStart != '' ? model.body.sessionStart : ' '}"> &#160;</fo:inline>
                    - 20
                    <fo:inline border-bottom="0.6pt solid black" padding-left="6pt" padding-right="6pt"
                               th:text="${model.body.sessionEnd != null and model.body.sessionEnd != '' ? model.body.sessionEnd : ' '}"> &#160;</fo:inline>
                </fo:block>

                <fo:table border="none" width="100%" >
                    <fo:table-column column-width="22mm"/>
                    <fo:table-column column-width="150mm"/>
                    <fo:table-body>
                        <fo:table-row>
                            <!-- Logo Cell -->
                            <fo:table-cell display-align="center">
                                <fo:block text-align="left" padding-top="0mm">
                                    <fo:external-graphic
                                            src='url("https://s3.ap-southeast-1.wasabisys.com/wexl-strapi-images/doon-logo-with-name.png")'
                                            content-width="20mm"
                                            scaling="uniform"/>
                                </fo:block>
                            </fo:table-cell>

                            <!-- Text Cell -->
                            <fo:table-cell display-align="center">
                                <fo:block text-align="center">
                                    <!-- Full Name -->

                                    <fo:block font-size="12pt" space-after="5mm" font-weight="bold">
                                        Full Name : <fo:inline border-bottom="1pt dotted black" padding-right="224pt"
                                                               th:text="${model.body.name}">&#160;</fo:inline>
                                    </fo:block>
                                    <!-- Class & Section -->
                                    <fo:block font-size="12pt" font-weight="bold">
                                        Class :
                                        <fo:inline-container width="140pt">
                                            <fo:block border-bottom="0.6pt solid black"
                                                      th:text="${model.body.studentClass != null and model.body.studentClass != '' ? model.body.studentClass : ' '}">&#160;</fo:block>
                                        </fo:inline-container>
                                        Section :
                                        <fo:inline-container width="110pt">
                                            <fo:block border-bottom="0.6pt solid black"
                                                      th:text="${model.body.section != null and model.body.section != '' ? model.body.section : ' '}">&#160;</fo:block>
                                        </fo:inline-container>
                                    </fo:block>
                                </fo:block>
                            </fo:table-cell>
                        </fo:table-row>
                    </fo:table-body>
                </fo:table>

                <fo:block text-align="center" space-after="2mm"
                          font-size="16pt" font-weight="bold" color="#15113d" font-family="Times New Roman, serif">
                    LEARNING VOYAGE
                </fo:block>

                <!-- Term 1 Table -->
                <fo:block space-after="2mm" th:if="${model.body.term != 't2'}">
                    <fo:table border="1pt solid black">
                        <fo:table-column column-width="75mm" border="1pt solid black"/>
                        <fo:table-column column-width="50mm" border="1pt solid black"/>
                        <fo:table-column column-width="50mm" border="1pt solid black"/>
                        <fo:table-header>
                            <fo:table-row border="1pt solid black" font-weight="bold" font-size="12pt" text-align="center">
                                <fo:table-cell number-rows-spanned="2" padding-top="5mm" font-size="13pt">
                                    <fo:block> SUBJECT </fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding="1mm" >
                                    <fo:block> Lunchpad Learning</fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding="1mm">
                                    <fo:block> Mid Mastery Triumph</fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                            <fo:table-row border="1pt solid black" font-weight="bold" font-size="12pt" text-align="center">
                                <fo:table-cell padding="2mm">
                                    <fo:block> (W+O)</fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding="2mm">
                                    <fo:block> (W+O)</fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-header>
                        <fo:table-body font-size="12pt">
                            <!-- data empty -->
                            <fo:table-row  th:if="${model.body.learningVoyage == null or #lists.isEmpty(model.body.learningVoyage)}">
                                <fo:table-cell height="10mm" border="1pt solid black" text-align="center">
                                    <fo:block> </fo:block>
                                </fo:table-cell>
                                <fo:table-cell height="10mm" border="1pt solid black" text-align="center">
                                    <fo:block> </fo:block>
                                </fo:table-cell>
                                <fo:table-cell height="10mm" border="1pt solid black" text-align="center">
                                    <fo:block> </fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                            <!-- data not empty -->
                            <fo:table-row text-align="center" border="1pt solid black" th:if="${model.body.learningVoyage != null and not #lists.isEmpty(model.body.learningVoyage)}"
                                          th:each="data : ${model.body.learningVoyage}">
                                <fo:table-cell  padding="2mm" >
                                    <fo:block th:text="${#strings.toUpperCase(data.subject)}"> </fo:block>
                                </fo:table-cell>
                                <fo:table-cell  padding="2mm">
                                    <fo:block th:text="${data.ll}"> </fo:block>
                                </fo:table-cell>
                                <fo:table-cell  padding="2mm">
                                    <fo:block th:text="${data.mmt}"> </fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>
                </fo:block>

                <!-- Yearly Table -->
                <fo:block space-after="2mm" th:if="${model.body.term == 't2'}">
                    <fo:table border="1pt solid black">
                        <fo:table-column column-width="35mm" border="1pt solid black"/>
                        <fo:table-column column-width="35mm" border="1pt solid black"/>
                        <fo:table-column column-width="35mm" border="1pt solid black"/>
                        <fo:table-column column-width="35mm" border="1pt solid black"/>
                        <fo:table-column column-width="35mm" border="1pt solid black"/>
                        <fo:table-header>
                            <fo:table-row border="1pt solid black" font-weight="bold" font-size="12pt" text-align="center">
                                <fo:table-cell number-rows-spanned="2" padding-top="12mm" font-size="13pt">
                                    <fo:block> SUBJECT </fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding="1mm" >
                                    <fo:block> Lunchpad Learning</fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding="1mm">
                                    <fo:block> Mid Mastery Triumph</fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding="1mm">
                                    <fo:block> Advancing Anchors</fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding="1mm">
                                    <fo:block> Yearly Knowledge Voyage</fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                            <fo:table-row border="1pt solid black" font-weight="bold" font-size="12pt" text-align="center">
                                <fo:table-cell padding="2mm">
                                    <fo:block> (W+O)</fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding="2mm">
                                    <fo:block> (W+O)</fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding="2mm">
                                    <fo:block> (W+O)</fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding="2mm">
                                    <fo:block> (W+O)</fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-header>
                        <fo:table-body font-size="12pt">
                            <!-- data empty -->
                            <fo:table-row th:if="${model.body.learningVoyage == null or #lists.isEmpty(model.body.learningVoyage)}">
                                <fo:table-cell height="10mm" border="1pt solid black" text-align="center">
                                    <fo:block> </fo:block>
                                </fo:table-cell>
                                <fo:table-cell height="10mm" border="1pt solid black" text-align="center">
                                    <fo:block> </fo:block>
                                </fo:table-cell>
                                <fo:table-cell height="10mm" border="1pt solid black" text-align="center">
                                    <fo:block> </fo:block>
                                </fo:table-cell>
                                <fo:table-cell height="10mm" border="1pt solid black" text-align="center">
                                    <fo:block> </fo:block>
                                </fo:table-cell>
                                <fo:table-cell height="10mm" border="1pt solid black" text-align="center">
                                    <fo:block> </fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                            <!-- data not empty -->
                            <fo:table-row text-align="center" border="1pt solid black" th:if="${model.body.learningVoyage != null and not #lists.isEmpty(model.body.learningVoyage)}"
                                          th:each="data : ${model.body.learningVoyage}">
                                <fo:table-cell  padding="2mm" >
                                    <fo:block th:text="${#strings.toUpperCase(data.subject)}"> </fo:block>
                                </fo:table-cell>
                                <fo:table-cell  padding="2mm">
                                    <fo:block th:text="${data.ll}"> </fo:block>
                                </fo:table-cell>
                                <fo:table-cell  padding="2mm">
                                    <fo:block th:text="${data.mmt}"> </fo:block>
                                </fo:table-cell>
                                <fo:table-cell  padding="2mm">
                                    <fo:block th:text="${data.aa}"> </fo:block>
                                </fo:table-cell>
                                <fo:table-cell  padding="2mm">
                                    <fo:block th:text="${data.ykv}"> </fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>
                </fo:block>

                <!-- Term 1 Remarks -->
                <fo:block th:if="${model.body.term != 't2'}">
                    <fo:table border="1pt solid black">
                        <fo:table-column column-width="75mm" border="1pt solid black"/>
                        <fo:table-column column-width="100mm" border="1pt solid black"/>
                        <fo:table-header>
                            <fo:table-row border="1pt solid black" font-weight="bold" font-size="12pt" text-align="center" height="11mm">
                                <fo:table-cell number-rows-spanned="2" padding-top="12mm" font-size="13pt">
                                    <fo:block> REMARKS </fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding-top="5mm" font-size="12pt">
                                    <fo:block> Mid Mastery Triumph</fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                            <fo:table-row border="1pt solid black" font-weight="bold" font-size="12pt" text-align="center" height="11mm">
                                <fo:table-cell padding="2mm">
                                    <fo:block> </fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-header>
                        <fo:table-body font-size="12pt">
                            <fo:table-row border="1pt solid black" font-weight="bold" font-size="12pt" text-align="center" height="11mm">
                                <fo:table-cell padding="3pt">
                                    <fo:block> Teacher's Signature </fo:block>
                                </fo:table-cell>
                                <fo:table-cell >
                                    <fo:block> </fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                            <fo:table-row border="1pt solid black" font-weight="bold" font-size="12pt" text-align="center" height="11mm">
                                <fo:table-cell padding="3pt">
                                    <fo:block> Supervisor's Signature </fo:block>
                                </fo:table-cell>
                                <fo:table-cell >
                                    <fo:block> </fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                            <fo:table-row border="1pt solid black" font-weight="bold" font-size="12pt" text-align="center" height="11mm">
                                <fo:table-cell padding="3pt">
                                    <fo:block> Incharge's Signature </fo:block>
                                </fo:table-cell>
                                <fo:table-cell >
                                    <fo:block> </fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                            <fo:table-row border="1pt solid black" font-weight="bold" font-size="12pt" text-align="center" height="11mm">
                                <fo:table-cell padding="3pt">
                                    <fo:block> Principal's Signature </fo:block>
                                </fo:table-cell>
                                <fo:table-cell >
                                    <fo:block> </fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                            <fo:table-row border="1pt solid black" font-weight="bold" font-size="12pt" text-align="center" height="11mm">
                                <fo:table-cell padding="3pt">
                                    <fo:block> Guardian's Signature </fo:block>
                                </fo:table-cell>
                                <fo:table-cell >
                                    <fo:block> </fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>
                </fo:block>

                <!-- Yearly Remarks -->
                <fo:block th:if="${model.body.term == 't2'}">
                    <fo:table border="1pt solid black">
                        <fo:table-column column-width="35mm" border="1pt solid black"/>
                        <fo:table-column column-width="70mm" border="1pt solid black"/>
                        <fo:table-column column-width="70mm" border="1pt solid black"/>
                        <fo:table-header>
                            <fo:table-row border="1pt solid black" font-weight="bold" font-size="12pt" text-align="center" height="11mm">
                                <fo:table-cell number-rows-spanned="2" padding-top="12mm" font-size="13pt">
                                    <fo:block> REMARKS </fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding-top="5mm" font-size="12pt">
                                    <fo:block> Mid Mastery Triumph</fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding-top="5mm" font-size="12pt">
                                    <fo:block> Yearly Knowledge Voyage</fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                            <fo:table-row border="1pt solid black" font-weight="bold" font-size="12pt" text-align="center" height="11mm">
                                <fo:table-cell padding="2mm">
                                    <fo:block> </fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding="2mm">
                                    <fo:block> </fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-header>
                        <fo:table-body font-size="12pt">
                            <fo:table-row border="1pt solid black" font-weight="bold" font-size="12pt" text-align="center" height="11mm">
                                <fo:table-cell padding="3pt">
                                    <fo:block> Teacher's Signature </fo:block>
                                </fo:table-cell>
                                <fo:table-cell >
                                    <fo:block> </fo:block>
                                </fo:table-cell>
                                <fo:table-cell >
                                    <fo:block> </fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                            <fo:table-row border="1pt solid black" font-weight="bold" font-size="12pt" text-align="center" height="11mm">
                                <fo:table-cell padding="3pt">
                                    <fo:block> Supervisor's Signature </fo:block>
                                </fo:table-cell>
                                <fo:table-cell >
                                    <fo:block> </fo:block>
                                </fo:table-cell>
                                <fo:table-cell >
                                    <fo:block> </fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                            <fo:table-row border="1pt solid black" font-weight="bold" font-size="12pt" text-align="center" height="11mm">
                                <fo:table-cell padding="3pt">
                                    <fo:block> Incharge's Signature </fo:block>
                                </fo:table-cell>
                                <fo:table-cell >
                                    <fo:block> </fo:block>
                                </fo:table-cell>
                                <fo:table-cell >
                                    <fo:block> </fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                            <fo:table-row border="1pt solid black" font-weight="bold" font-size="12pt" text-align="center" height="11mm">
                                <fo:table-cell padding="3pt">
                                    <fo:block> Principal's Signature </fo:block>
                                </fo:table-cell>
                                <fo:table-cell >
                                    <fo:block> </fo:block>
                                </fo:table-cell>
                                <fo:table-cell >
                                    <fo:block> </fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                            <fo:table-row border="1pt solid black" font-weight="bold" font-size="12pt" text-align="center" height="11mm">
                                <fo:table-cell padding="3pt">
                                    <fo:block> Guardian's Signature </fo:block>
                                </fo:table-cell>
                                <fo:table-cell >
                                    <fo:block> </fo:block>
                                </fo:table-cell>
                                <fo:table-cell >
                                    <fo:block> </fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>
                </fo:block>

            </fo:block-container>
        </fo:flow>
    </fo:page-sequence>

    <!-- 3rd Page -->

    <fo:page-sequence master-reference="invoice">
        <fo:flow flow-name="xsl-region-body">
            <fo:block-container width="100%" height="100%" margin-top="-0.5cm" padding="6mm">

                <!-- Background Watermark -->
                <fo:block-container absolute-position="absolute" top="-40%" left="-14%" width="0%" height="0%">
                    <fo:block text-align="center" color="white" font-size="48pt" font-family="Arial">
                        <fo:instream-foreign-object content-width="280%" content-height="280%">
                            <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"
                                 width="440%" height="440%" viewBox="0 0 100 100">
                                <image x="0" y="0" width="100%" height="100%"
                                       xlink:href="https://s3.ap-southeast-1.wasabisys.com/wexl-strapi-images/doon%20bharati%20report%20card/background%20nur-ukg.png"/>
                            </svg>
                        </fo:instream-foreign-object>
                    </fo:block>
                </fo:block-container>

                <!-- Logo -->
                <fo:block text-align="right" padding-top="-10mm">
                    <fo:external-graphic
                            src='url("https://s3.ap-southeast-1.wasabisys.com/wexl-strapi-images/doon-logo-with-name.png")'
                            content-width="50px"
                            content-height="auto"
                            scaling="uniform"/>
                </fo:block>

                <!-- Title -->
                <fo:block text-align="center" font-size="14pt" font-weight="bold" padding-top="-25mm" margin-top="2mm">
                    Session 20<fo:inline border-bottom="0.6pt solid black" padding-left="6pt" padding-right="6pt"
                                         th:text="${model.body.sessionStart != null and model.body.sessionStart != '' ? model.body.sessionStart : ' '}"> &#160;</fo:inline>
                    - 20
                    <fo:inline border-bottom="0.6pt solid black" padding-left="6pt" padding-right="6pt"
                               th:text="${model.body.sessionEnd != null and model.body.sessionEnd != '' ? model.body.sessionEnd : ' '}"> &#160;</fo:inline>
                </fo:block>

                <fo:block text-align="center" margin-top="2mm"
                          font-size="14pt" font-weight="bold">
                    (Based on 5 Point Grading System)
                </fo:block>

                <!-- Term1 Layout -->
                <fo:block space-before="3pt" th:if="${model.body.term != 't2'}">
                    <fo:table width="100%" table-layout="fixed">
                        <fo:table-column column-width="48%"/>
                        <fo:table-column column-width="2%"/>
                        <fo:table-column column-width="48%"/>
                        <fo:table-body>
                            <fo:table-row>

                                <!-- Left Table -->
                                <fo:table-cell>
                                    <fo:block font-size="11pt" font-weight="bold" space-before="2pt">Early Child Care Development </fo:block>
                                    <fo:table border="1pt solid black" width="100%" table-layout="fixed">
                                        <fo:table-column column-width="48%" border="1pt solid black"/>
                                        <fo:table-column column-width="26%" border="1pt dotted black"/>
                                        <fo:table-column column-width="26%" border="1pt solid black"/>
                                        <fo:table-header>

                                            <!-- Header Row -->
                                            <fo:table-row font-size="9pt" font-weight="bold" text-align="center">
                                                <fo:table-cell number-rows-spanned="2">
                                                    <fo:block font-weight="bold" padding-top="6mm">Milestone</fo:block>
                                                </fo:table-cell>
                                                <fo:table-cell number-columns-spanned="2">
                                                    <fo:block font-weight="bold">Mid Mastery Triumph</fo:block>
                                                </fo:table-cell>
                                            </fo:table-row>
                                            <fo:table-row border="1pt solid black" font-size="9pt" font-weight="bold" text-align="center">
                                                <fo:table-cell >
                                                    <fo:block font-weight="bold">English</fo:block>
                                                </fo:table-cell>
                                                <fo:table-cell >
                                                    <fo:block font-weight="bold">Hindi</fo:block>
                                                </fo:table-cell>
                                            </fo:table-row>
                                        </fo:table-header>

                                        <fo:table-body font-size="10pt">
                                            <!-- Listening Section -->
                                            <fo:table-row >
                                                <fo:table-cell padding="1mm">
                                                    <fo:block font-weight="bold" >LISTENING</fo:block>
                                                </fo:table-cell>
                                                <fo:table-cell padding="1mm">
                                                    <fo:block> </fo:block>
                                                </fo:table-cell>
                                                <fo:table-cell padding="1mm">
                                                    <fo:block> </fo:block>
                                                </fo:table-cell>
                                            </fo:table-row>
                                            <!-- If data is empty -->
                                            <th:block th:if="${#lists.isEmpty(model.body.leftTable.listening.data)
                                                or model.body.leftTable.listening.data.size() == 0}">
                                                <fo:table-row>
                                                    <fo:table-cell padding="1mm"><fo:block></fo:block></fo:table-cell>
                                                    <fo:table-cell padding="1mm"><fo:block></fo:block></fo:table-cell>
                                                    <fo:table-cell padding="1mm"><fo:block></fo:block></fo:table-cell>
                                                </fo:table-row>
                                            </th:block>


                                            <th:block th:if="${!#lists.isEmpty(model.body.leftTable.listening.data) and model.body.leftTable.listening.data.size() != 0}">
                                                <th:block th:each="row : ${model.body.leftTable.listening.data}">
                                                    <fo:table-row>
                                                        <fo:table-cell padding="1mm">
                                                            <fo:block th:text="${row.milestone}"> </fo:block>
                                                        </fo:table-cell>
                                                        <fo:table-cell padding="1mm">
                                                            <fo:block th:text="${row.english_mmt}"/>
                                                        </fo:table-cell>
                                                        <fo:table-cell padding="1mm">
                                                            <fo:block th:text="${row.hindi_mmt}"/>
                                                        </fo:table-cell>
                                                    </fo:table-row>
                                                </th:block>
                                            </th:block>

                                            <!-- Reading Section -->
                                            <fo:table-row>
                                                <fo:table-cell padding="1mm">
                                                    <fo:block font-weight="bold">READING</fo:block>
                                                </fo:table-cell >
                                                <fo:table-cell padding="1mm">
                                                    <fo:block> </fo:block>
                                                </fo:table-cell>
                                                <fo:table-cell padding="1mm">
                                                    <fo:block> </fo:block>
                                                </fo:table-cell>
                                            </fo:table-row>

                                            <!-- If data is empty -->
                                            <th:block th:if="${#lists.isEmpty(model.body.leftTable.reading.data)
                                                 or model.body.leftTable.reading.data.size() == 0}">
                                                <fo:table-row>
                                                    <fo:table-cell padding="1mm"><fo:block></fo:block></fo:table-cell>
                                                    <fo:table-cell padding="1mm"><fo:block></fo:block></fo:table-cell>
                                                    <fo:table-cell padding="1mm"><fo:block></fo:block></fo:table-cell>
                                                </fo:table-row>
                                            </th:block>

                                            <th:block th:if="${!#lists.isEmpty(model.body.leftTable.reading.data) and model.body.leftTable.reading.data.size() != 0}">
                                                <th:block th:each="row : ${model.body.leftTable.reading.data}">
                                                    <fo:table-row>
                                                        <fo:table-cell padding="1mm">
                                                            <fo:block th:text="${row.milestone}"> </fo:block>
                                                        </fo:table-cell>
                                                        <fo:table-cell padding="1mm">
                                                            <fo:block th:text="${row.english_mmt}"/>
                                                        </fo:table-cell>
                                                        <fo:table-cell padding="1mm">
                                                            <fo:block th:text="${row.hindi_mmt}"/>
                                                        </fo:table-cell>
                                                    </fo:table-row>
                                                </th:block>
                                            </th:block>

                                            <!-- Writing Section -->
                                            <fo:table-row>
                                                <fo:table-cell padding="1mm">
                                                    <fo:block font-weight="bold">WRITING</fo:block>
                                                </fo:table-cell>
                                                <fo:table-cell padding="1mm">
                                                    <fo:block> </fo:block>
                                                </fo:table-cell>
                                                <fo:table-cell padding="1mm">
                                                    <fo:block> </fo:block>
                                                </fo:table-cell>
                                            </fo:table-row>

                                            <!-- If data is empty -->
                                            <th:block th:if="${#lists.isEmpty(model.body.leftTable.writing.data)
                                                 or model.body.leftTable.writing.data.size() == 0}">
                                                <fo:table-row>
                                                    <fo:table-cell padding="1mm"><fo:block></fo:block></fo:table-cell>
                                                    <fo:table-cell padding="1mm"><fo:block></fo:block></fo:table-cell>
                                                    <fo:table-cell padding="1mm"><fo:block></fo:block></fo:table-cell>
                                                </fo:table-row>
                                            </th:block>

                                            <th:block th:if="${!#lists.isEmpty(model.body.leftTable.writing.data) and model.body.leftTable.writing.data.size() != 0}">
                                                <th:block th:each="row : ${model.body.leftTable.writing.data}">
                                                    <fo:table-row>
                                                        <fo:table-cell padding="1mm">
                                                            <fo:block th:text="${row.milestone}"> </fo:block>
                                                        </fo:table-cell>
                                                        <fo:table-cell padding="1mm">
                                                            <fo:block th:text="${row.english_mmt}"/>
                                                        </fo:table-cell>
                                                        <fo:table-cell padding="1mm">
                                                            <fo:block th:text="${row.hindi_mmt}"/>
                                                        </fo:table-cell>
                                                    </fo:table-row>
                                                </th:block>
                                            </th:block>

                                        </fo:table-body>
                                    </fo:table>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block> </fo:block>
                                </fo:table-cell>

                                <!-- Right Table -->
                                <fo:table-cell>
                                    <fo:block> .</fo:block>
                                    <!-- Right Table (single header; keep column borders) -->
                                    <fo:table border="1pt solid black" width="100%" table-layout="fixed">
                                        <!-- keep your column borders exactly as requested -->
                                        <fo:table-column column-width="40%" border="1pt solid black"/>
                                        <fo:table-column column-width="60%" border="1pt solid black"/>

                                        <!-- Header once -->
                                        <fo:table-header>
                                            <fo:table-row border="1pt solid black" font-size="10pt" font-weight="bold" text-align="center">
                                                <fo:table-cell padding="2pt">
                                                    <fo:block>Milestone</fo:block>
                                                </fo:table-cell>
                                                <fo:table-cell padding="2pt">
                                                    <fo:block>Mid Mastery Triumph</fo:block>
                                                </fo:table-cell>
                                            </fo:table-row>
                                        </fo:table-header>

                                        <fo:table-body font-size="10pt">
                                            <!-- iterate sections; we’ll insert a spacer row AFTER each section (except the last) -->
                                            <!-- ✅ Case 1: When rightTable has data -->
                                            <th:block th:if="${!#lists.isEmpty(model.body.rightTable)}">
                                                <th:block th:each="section, st : ${model.body.rightTable}">

                                                    <!-- Section title row -->
                                                    <fo:table-row>
                                                        <fo:table-cell number-columns-spanned="2" padding="3pt">
                                                            <fo:block font-weight="bold" th:text="${section.name}"/>
                                                        </fo:table-cell>
                                                    </fo:table-row>

                                                    <!-- Section data rows -->
                                                    <th:block th:each="row : ${section.details}">
                                                        <fo:table-row>
                                                            <fo:table-cell padding="2pt" border="1pt solid black">
                                                                <fo:block th:text="${row.milestone}"/>
                                                            </fo:table-cell>
                                                            <fo:table-cell padding="3pt" text-align="center" border="1pt solid black">
                                                                <fo:block th:text="${row.mmt != null ? row.mmt : ''}"/>
                                                            </fo:table-cell>
                                                        </fo:table-row>
                                                    </th:block>

                                                </th:block>
                                            </th:block>


                                            <!-- ✅ Case 2: When rightTable is empty -->
                                            <th:block th:if="${#lists.isEmpty(model.body.rightTable)}">
                                                <fo:table-row height="6mm">
                                                    <fo:table-cell padding="2pt" border="1pt solid black">
                                                        <fo:block></fo:block>
                                                    </fo:table-cell>
                                                    <fo:table-cell padding="2pt" border="1pt solid black">
                                                        <fo:block></fo:block>
                                                    </fo:table-cell>
                                                </fo:table-row>
                                            </th:block>
                                        </fo:table-body>
                                    </fo:table>
                                </fo:table-cell>

                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>
                </fo:block>

                <!-- Yearly Layout -->
                <fo:block space-before="3pt" th:if="${model.body.term == 't2'}">
                    <fo:table width="100%" table-layout="fixed">
                        <fo:table-column column-width="48%"/>
                        <fo:table-column column-width="2%"/>
                        <fo:table-column column-width="48%"/>
                        <fo:table-body>
                            <fo:table-row>

                                <!-- Left Table -->
                                <fo:table-cell>
                                    <fo:block font-size="11pt" font-weight="bold" space-before="2pt">Early Child Care Development </fo:block>
                                    <fo:table border="1pt solid black" width="100%" table-layout="fixed">
                                        <fo:table-column column-width="48%" border="1pt solid black"/>
                                        <fo:table-column column-width="13%" border="1pt dotted black"/>
                                        <fo:table-column column-width="13%" border="1pt dotted black"/>
                                        <fo:table-column column-width="13%" border="1pt dotted black"/>
                                        <fo:table-column column-width="13%" border="1pt dotted black"/>
                                        <fo:table-header>

                                            <!-- Header Row -->
                                            <fo:table-row font-size="9pt" font-weight="bold" text-align="center">
                                                <fo:table-cell number-rows-spanned="2">
                                                    <fo:block font-weight="bold" padding-top="6mm">Milestone</fo:block>
                                                </fo:table-cell>
                                                <fo:table-cell number-columns-spanned="2">
                                                    <fo:block font-weight="bold">Mid Mastery Triumph</fo:block>
                                                </fo:table-cell>
                                                <fo:table-cell number-columns-spanned="2">
                                                    <fo:block font-weight="bold">Yearly Knowledge Voyage</fo:block>
                                                </fo:table-cell>
                                            </fo:table-row>
                                            <fo:table-row border="1pt solid black" font-size="9pt" font-weight="bold" text-align="center">
                                                <fo:table-cell >
                                                    <fo:block font-weight="bold">English</fo:block>
                                                </fo:table-cell>
                                                <fo:table-cell >
                                                    <fo:block font-weight="bold">Hindi</fo:block>
                                                </fo:table-cell>
                                                <fo:table-cell >
                                                    <fo:block font-weight="bold">English</fo:block>
                                                </fo:table-cell>
                                                <fo:table-cell >
                                                    <fo:block font-weight="bold">Hindi</fo:block>
                                                </fo:table-cell>
                                            </fo:table-row>
                                        </fo:table-header>

                                        <fo:table-body font-size="10pt">
                                            <!-- Listening Section -->
                                            <fo:table-row >
                                                <fo:table-cell padding="1mm">
                                                    <fo:block font-weight="bold" >LISTENING</fo:block>
                                                </fo:table-cell>
                                                <fo:table-cell padding="1mm">
                                                    <fo:block> </fo:block>
                                                </fo:table-cell>
                                                <fo:table-cell padding="1mm">
                                                    <fo:block> </fo:block>
                                                </fo:table-cell>
                                                <fo:table-cell padding="1mm">
                                                    <fo:block> </fo:block>
                                                </fo:table-cell>
                                                <fo:table-cell padding="1mm">
                                                    <fo:block> </fo:block>
                                                </fo:table-cell>
                                            </fo:table-row>
                                            <!-- If data is empty -->
                                            <th:block th:if="${#lists.isEmpty(model.body.leftTable.listening.data)}">
                                                <fo:table-row>
                                                    <fo:table-cell padding="1mm"><fo:block></fo:block></fo:table-cell>
                                                    <fo:table-cell padding="1mm"><fo:block></fo:block></fo:table-cell>
                                                    <fo:table-cell padding="1mm"><fo:block></fo:block></fo:table-cell>
                                                    <fo:table-cell padding="1mm"><fo:block></fo:block></fo:table-cell>
                                                    <fo:table-cell padding="1mm"><fo:block></fo:block></fo:table-cell>
                                                </fo:table-row>
                                            </th:block>

                                            <th:block th:if="${!#lists.isEmpty(model.body.leftTable.listening.data)}">
                                                <th:block th:each="row : ${model.body.leftTable.listening.data}">
                                                    <fo:table-row>
                                                        <fo:table-cell padding="1mm">
                                                            <fo:block th:text="${row.milestone}"> </fo:block>
                                                        </fo:table-cell>
                                                        <fo:table-cell padding="1mm">
                                                            <fo:block th:text="${row.english_mmt}"/>
                                                        </fo:table-cell>
                                                        <fo:table-cell padding="1mm">
                                                            <fo:block th:text="${row.hindi_mmt}"/>
                                                        </fo:table-cell>
                                                        <fo:table-cell padding="1mm">
                                                            <fo:block th:text="${row.english_ykv}"/>
                                                        </fo:table-cell>
                                                        <fo:table-cell padding="1mm">
                                                            <fo:block th:text="${row.hindi_ykv}"/>
                                                        </fo:table-cell>
                                                    </fo:table-row>
                                                </th:block>
                                            </th:block>

                                            <!-- Reading Section -->
                                            <fo:table-row>
                                                <fo:table-cell padding="1mm">
                                                    <fo:block font-weight="bold">READING</fo:block>
                                                </fo:table-cell >
                                                <fo:table-cell padding="1mm">
                                                    <fo:block> </fo:block>
                                                </fo:table-cell>
                                                <fo:table-cell padding="1mm">
                                                    <fo:block> </fo:block>
                                                </fo:table-cell>
                                                <fo:table-cell padding="1mm">
                                                    <fo:block> </fo:block>
                                                </fo:table-cell>
                                                <fo:table-cell padding="1mm">
                                                    <fo:block> </fo:block>
                                                </fo:table-cell>
                                            </fo:table-row>

                                            <!-- If data is empty -->
                                            <th:block th:if="${#lists.isEmpty(model.body.leftTable.reading.data)}">
                                                <fo:table-row>
                                                    <fo:table-cell padding="1mm"><fo:block></fo:block></fo:table-cell>
                                                    <fo:table-cell padding="1mm"><fo:block></fo:block></fo:table-cell>
                                                    <fo:table-cell padding="1mm"><fo:block></fo:block></fo:table-cell>
                                                    <fo:table-cell padding="1mm"><fo:block></fo:block></fo:table-cell>
                                                    <fo:table-cell padding="1mm"><fo:block></fo:block></fo:table-cell>
                                                </fo:table-row>
                                            </th:block>

                                            <th:block th:if="${!#lists.isEmpty(model.body.leftTable.reading.data)}">
                                                <th:block th:each="row : ${model.body.leftTable.reading.data}">
                                                    <fo:table-row>
                                                        <fo:table-cell padding="1mm">
                                                            <fo:block th:text="${row.milestone}"> </fo:block>
                                                        </fo:table-cell>
                                                        <fo:table-cell padding="1mm">
                                                            <fo:block th:text="${row.english_mmt}"/>
                                                        </fo:table-cell>
                                                        <fo:table-cell padding="1mm">
                                                            <fo:block th:text="${row.hindi_mmt}"/>
                                                        </fo:table-cell>
                                                        <fo:table-cell padding="1mm">
                                                            <fo:block th:text="${row.english_ykv}"/>
                                                        </fo:table-cell>
                                                        <fo:table-cell padding="1mm">
                                                            <fo:block th:text="${row.hindi_ykv}"/>
                                                        </fo:table-cell>
                                                    </fo:table-row>
                                                </th:block>
                                            </th:block>

                                            <!-- Writing Section -->
                                            <fo:table-row>
                                                <fo:table-cell padding="1mm">
                                                    <fo:block font-weight="bold">WRITING</fo:block>
                                                </fo:table-cell>
                                                <fo:table-cell padding="1mm">
                                                    <fo:block> </fo:block>
                                                </fo:table-cell>
                                                <fo:table-cell padding="1mm">
                                                    <fo:block> </fo:block>
                                                </fo:table-cell>
                                                <fo:table-cell padding="1mm">
                                                    <fo:block> </fo:block>
                                                </fo:table-cell>
                                                <fo:table-cell padding="1mm">
                                                    <fo:block> </fo:block>
                                                </fo:table-cell>
                                            </fo:table-row>

                                            <!-- If data is empty -->
                                            <th:block th:if="${#lists.isEmpty(model.body.leftTable.writing.data)}">
                                                <fo:table-row>
                                                    <fo:table-cell padding="1mm"><fo:block></fo:block></fo:table-cell>
                                                    <fo:table-cell padding="1mm"><fo:block></fo:block></fo:table-cell>
                                                    <fo:table-cell padding="1mm"><fo:block></fo:block></fo:table-cell>
                                                    <fo:table-cell padding="1mm"><fo:block></fo:block></fo:table-cell>
                                                    <fo:table-cell padding="1mm"><fo:block></fo:block></fo:table-cell>
                                                </fo:table-row>
                                            </th:block>

                                            <th:block th:if="${!#lists.isEmpty(model.body.leftTable.writing.data)}">
                                                <th:block th:each="row : ${model.body.leftTable.writing.data}">
                                                    <fo:table-row>
                                                        <fo:table-cell padding="1mm">
                                                            <fo:block th:text="${row.milestone}"> </fo:block>
                                                        </fo:table-cell>
                                                        <fo:table-cell padding="1mm">
                                                            <fo:block th:text="${row.english_mmt}"/>
                                                        </fo:table-cell>
                                                        <fo:table-cell padding="1mm">
                                                            <fo:block th:text="${row.hindi_mmt}"/>
                                                        </fo:table-cell>
                                                        <fo:table-cell padding="1mm">
                                                            <fo:block th:text="${row.english_ykv}"/>
                                                        </fo:table-cell>
                                                        <fo:table-cell padding="1mm">
                                                            <fo:block th:text="${row.hindi_ykv}"/>
                                                        </fo:table-cell>
                                                    </fo:table-row>
                                                </th:block>
                                            </th:block>

                                        </fo:table-body>
                                    </fo:table>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block> </fo:block>
                                </fo:table-cell>

                                <!-- Right Table -->
                                <fo:table-cell>
                                    <fo:block> .</fo:block>
                                    <!-- Right Table (single header; keep column borders) -->
                                    <fo:table border="1pt solid black" width="100%" table-layout="fixed">
                                        <!-- keep your column borders exactly as requested -->
                                        <fo:table-column column-width="40%" border="1pt solid black"/>
                                        <fo:table-column column-width="30%" border="1pt solid black"/>
                                        <fo:table-column column-width="30%" border="1pt solid black"/>

                                        <!-- Header once -->
                                        <fo:table-header>
                                            <fo:table-row border="1pt solid black" font-size="9pt" font-weight="bold" text-align="center">
                                                <fo:table-cell>
                                                    <fo:block>Milestone</fo:block>
                                                </fo:table-cell>
                                                <fo:table-cell>
                                                    <fo:block>Mid Mastery Triumph</fo:block>
                                                </fo:table-cell>
                                                <fo:table-cell>
                                                    <fo:block>Yearly Knowledge Voyage</fo:block>
                                                </fo:table-cell>
                                            </fo:table-row>
                                        </fo:table-header>

                                        <fo:table-body font-size="10pt">
                                            <!-- iterate sections; we’ll insert a spacer row AFTER each section (except the last) -->
                                            <!-- ✅ Case 1: When rightTable has data -->
                                            <th:block th:if="${!#lists.isEmpty(model.body.rightTable)}">
                                                <th:block th:each="section, st : ${model.body.rightTable}">

                                                    <!-- Section title row -->
                                                    <fo:table-row>
                                                        <fo:table-cell number-columns-spanned="3" padding="3pt">
                                                            <fo:block font-weight="bold" th:text="${section.name}"/>
                                                        </fo:table-cell>
                                                    </fo:table-row>

                                                    <!-- Section data rows -->
                                                    <th:block th:each="row : ${section.details}">
                                                        <fo:table-row>
                                                            <fo:table-cell padding="2pt" border="1pt solid black">
                                                                <fo:block th:text="${row.milestone}"/>
                                                            </fo:table-cell>
                                                            <fo:table-cell padding="2pt" text-align="center" border="1pt solid black">
                                                                <fo:block th:text="${row.mmt != null ? row.mmt : ''}"/>
                                                            </fo:table-cell>
                                                            <fo:table-cell padding="2pt" text-align="center" border="1pt solid black">
                                                                <fo:block th:text="${row.ykv != null ? row.ykv : ''}"/>
                                                            </fo:table-cell>
                                                        </fo:table-row>
                                                    </th:block>

                                                </th:block>
                                            </th:block>


                                            <!-- ✅ Case 2: When rightTable is empty -->
                                            <th:block th:if="${#lists.isEmpty(model.body.rightTable)}">
                                                <fo:table-row height="6mm">
                                                    <fo:table-cell padding="2pt" border="1pt solid black">
                                                        <fo:block></fo:block>
                                                    </fo:table-cell>
                                                    <fo:table-cell padding="2pt" border="1pt solid black">
                                                        <fo:block></fo:block>
                                                    </fo:table-cell>
                                                    <fo:table-cell padding="2pt" border="1pt solid black">
                                                        <fo:block></fo:block>
                                                    </fo:table-cell>
                                                </fo:table-row>
                                            </th:block>

                                        </fo:table-body>
                                    </fo:table>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>
                </fo:block>

                <!-- Term1 Mathematical Understanding -->
                <fo:block th:if="${model.body.term != 't2'}">
                    <fo:block font-size="11pt" font-weight="bold" space-before="2pt">Mathematics Understanding

                        <fo:inline font-size="11pt" padding-left="34mm" font-weight="normal">4.Participation in School activity(Mid Mastery) </fo:inline>
                    </fo:block>

                    <fo:table width="100%" table-layout="fixed">
                        <fo:table-column column-width="48%"/>
                        <fo:table-column column-width="2%"/>
                        <fo:table-column column-width="48%"/>
                        <fo:table-body>
                            <fo:table-row>

                                <!-- Left Table -->
                                <fo:table-cell>
                                    <fo:table border="1pt solid black" width="100%" table-layout="fixed">
                                        <fo:table-column column-width="52%" border="1pt solid black"/>
                                        <fo:table-column column-width="48%" border="1pt solid black"/>
                                        <fo:table-header>

                                            <!-- Header Row -->
                                            <fo:table-row font-size="9pt" font-weight="bold" text-align="center">
                                                <fo:table-cell padding="2pt">
                                                    <fo:block > </fo:block>
                                                </fo:table-cell>
                                                <fo:table-cell font-size="7pt" font-weight="bold">
                                                    <fo:block font-weight="bold">Mid Mastery Triumph</fo:block>
                                                </fo:table-cell>
                                            </fo:table-row>
                                        </fo:table-header>
                                        <fo:table-body font-size="10pt">
                                            <th:block th:if="${#lists.isEmpty(model.body.mathmaticalUnderstanding)}">
                                                <fo:table-row border="1pt solid black" height="6mm">
                                                    <fo:table-cell padding="2pt"> <fo:block> </fo:block> </fo:table-cell>
                                                    <fo:table-cell padding="2pt"> <fo:block> </fo:block> </fo:table-cell>
                                                </fo:table-row>
                                            </th:block>

                                            <!-- Loop through activities -->
                                            <th:block th:each="row : ${model.body.mathmaticalUnderstanding}">
                                                <fo:table-row border="1pt solid black">
                                                    <fo:table-cell padding="2pt">
                                                        <fo:block th:text="${row.activity}"> </fo:block>
                                                    </fo:table-cell>
                                                    <fo:table-cell padding="2pt">
                                                        <fo:block th:text="${row.mmt} ?: ''"/>
                                                    </fo:table-cell>
                                                </fo:table-row>
                                            </th:block>

                                        </fo:table-body>
                                    </fo:table>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block> </fo:block>
                                </fo:table-cell>

                                <!-- participation in school -->
                                <fo:table-cell>
                                    <fo:table>
                                        <fo:table-column column-width="100%"/>

                                        <fo:table-body font-size="10pt">
                                            <fo:table-row height="20mm">
                                                <fo:table-cell padding="2pt" border="1pt solid black">
                                                    <fo:block th:text="${model.body.schoolActivity}"> </fo:block>
                                                </fo:table-cell>
                                            </fo:table-row>
                                        </fo:table-body>
                                    </fo:table>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>
                </fo:block>

                <!-- Yearly Mathematical Table -->
                <fo:block th:if="${model.body.term == 't2'}">
                    <fo:block font-size="11pt" font-weight="bold" space-before="2pt">Mathematics Understanding

                        <fo:inline font-size="11pt" padding-left="34mm" font-weight="normal">4.Participation in School activity(Mid Mastery) </fo:inline>
                    </fo:block>

                    <fo:table width="100%" table-layout="fixed">
                        <fo:table-column column-width="48%"/>
                        <fo:table-column column-width="2%"/>
                        <fo:table-column column-width="48%"/>
                        <fo:table-body>
                            <fo:table-row>

                                <!-- Left Table -->
                                <fo:table-cell>
                                    <fo:table border="1pt solid black" width="100%" table-layout="fixed">
                                        <fo:table-column column-width="48%" border="1pt solid black"/>
                                        <fo:table-column column-width="26%" border="1pt solid black"/>
                                        <fo:table-column column-width="26%" border="1pt solid black"/>
                                        <fo:table-header>

                                            <!-- Header Row -->
                                            <fo:table-row font-size="9pt" font-weight="bold" text-align="center">
                                                <fo:table-cell padding="2pt">
                                                    <fo:block > </fo:block>
                                                </fo:table-cell>
                                                <fo:table-cell font-size="7pt" font-weight="bold">
                                                    <fo:block font-weight="bold">Mid Mastery Triumph</fo:block>
                                                </fo:table-cell>
                                                <fo:table-cell font-size="7pt" font-weight="bold">
                                                    <fo:block font-weight="bold">Yearly Knowledge Voyage</fo:block>
                                                </fo:table-cell>
                                            </fo:table-row>
                                        </fo:table-header>
                                        <fo:table-body font-size="10pt">
                                            <th:block th:if="${#lists.isEmpty(model.body.mathmaticalUnderstanding)}">
                                                <fo:table-row border="1pt solid black" height="6mm">
                                                    <fo:table-cell padding="2pt"> <fo:block> </fo:block> </fo:table-cell>
                                                    <fo:table-cell padding="2pt"> <fo:block> </fo:block> </fo:table-cell>
                                                    <fo:table-cell padding="2pt"> <fo:block> </fo:block> </fo:table-cell>
                                                </fo:table-row>
                                            </th:block>

                                            <!-- Loop through activities -->
                                            <th:block th:each="row : ${model.body.mathmaticalUnderstanding}">
                                                <fo:table-row border="1pt solid black">
                                                    <fo:table-cell padding="2pt">
                                                        <fo:block th:text="${row.activity}"> </fo:block>
                                                    </fo:table-cell>
                                                    <fo:table-cell padding="2pt">
                                                        <fo:block th:text="${row.mmt} ?: ''"/>
                                                    </fo:table-cell>
                                                    <fo:table-cell padding="2pt">
                                                        <fo:block th:text="${row.ykv} ?: ''"/>
                                                    </fo:table-cell>
                                                </fo:table-row>
                                            </th:block>

                                        </fo:table-body>
                                    </fo:table>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block> </fo:block>
                                </fo:table-cell>

                                <!-- participation in school -->
                                <fo:table-cell>
                                    <fo:table>
                                        <fo:table-column column-width="100%"/>

                                        <fo:table-body font-size="10pt">
                                            <fo:table-row height="10mm">
                                                <fo:table-cell padding="2pt" border="1pt solid black">
                                                    <fo:block th:text="${model.body.schoolActivity}"> </fo:block>
                                                </fo:table-cell>
                                            </fo:table-row>
                                            <fo:table-row height="10mm">
                                                <fo:table-cell padding="2pt" border="1pt solid black">
                                                    <fo:block> </fo:block>
                                                </fo:table-cell>
                                            </fo:table-row>
                                        </fo:table-body>
                                    </fo:table>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>
                </fo:block>

                <!-- Attendance Table -->
                <fo:block font-size="11pt" font-weight="bold" space-before="5pt"> </fo:block>
                <fo:table width="100%" table-layout="fixed">
                    <fo:table-column column-width="48%"/>
                    <fo:table-column column-width="2%"/>
                    <fo:table-column column-width="48%"/>
                    <fo:table-body>
                        <fo:table-row>

                            <!-- Left Table -->
                            <fo:table-cell>
                                <fo:table border="1pt solid black" width="100%" table-layout="fixed">
                                    <fo:table-column column-width="50%" border="1pt solid black"/>
                                    <fo:table-column column-width="50%" border="1pt solid black"/>
                                    <fo:table-header>

                                        <!-- Header Row -->
                                        <fo:table-row font-size="10pt" font-weight="bold" text-align="center">
                                            <fo:table-cell padding="2pt">
                                                <fo:block >Total Working Days</fo:block>
                                            </fo:table-cell>
                                            <fo:table-cell font-size="8pt" font-weight="bold" padding-top="3pt">
                                                <fo:block font-weight="bold">Total Attendance By Student</fo:block>
                                            </fo:table-cell>
                                        </fo:table-row>
                                    </fo:table-header>
                                    <fo:table-body>
                                        <fo:table-row border="1pt solid black" font-size="10pt" font-weight="bold" text-align="center" height="7mm">
                                            <fo:table-cell padding="5pt">
                                                <fo:block font-weight="bold" th:text="${model.body.totalAttendance}"> </fo:block>
                                            </fo:table-cell>
                                            <fo:table-cell padding="5pt">
                                                <fo:block font-weight="bold" th:text="${model.body.studentAttendance}"> </fo:block>
                                            </fo:table-cell>
                                        </fo:table-row>
                                    </fo:table-body>
                                </fo:table>
                            </fo:table-cell>
                            <fo:table-cell>
                                <fo:block> </fo:block>
                            </fo:table-cell>
                            <fo:table-cell>
                                <fo:block space-before="2pt" font-size="11pt">5. Health Status </fo:block>
                                <fo:block margin-left="5mm" space-before="2pt" font-size="11pt">Height:
                                    <fo:inline-container width="60pt">
                                        <fo:block border-bottom="0.6pt dotted black"
                                                  th:text="${model.body.term1Height}">&#160;</fo:block>
                                    </fo:inline-container>
                                    <fo:inline-container width="60pt">
                                        <fo:block border-bottom="0.6pt dotted black"
                                                  th:text="${model.body.term2Height}">&#160;</fo:block>
                                    </fo:inline-container>
                                </fo:block>
                                <fo:block margin-left="5mm" space-before="2pt" font-size="11pt">Weight:
                                    <fo:inline-container width="58pt">
                                        <fo:block border-bottom="0.6pt dotted black"
                                                  th:text="${model.body.term1Weight}">&#160;</fo:block>
                                    </fo:inline-container>
                                    <fo:inline-container width="60pt">
                                        <fo:block border-bottom="0.6pt dotted black"
                                                  th:text="${model.body.term2Weight}">&#160;</fo:block>
                                    </fo:inline-container>
                                </fo:block>
                            </fo:table-cell>
                        </fo:table-row>
                    </fo:table-body>
                </fo:table>

                <fo:block space-before="8pt" space-after="2pt" font-size="10pt" th:if="${model.body.term == 't2'}">
                    Overall Remarks
                    <fo:inline-container width="410pt">
                        <fo:block border-bottom="0.6pt dotted black"
                                  th:text="${model.body.remarks}">&#160;</fo:block>
                    </fo:inline-container>
                </fo:block>

                <fo:block margin-left="5mm" space-before="3pt" space-after="2pt" font-size="10pt"
                          th:if="${model.body.term == 't2'}">(Year End)
                    <fo:inline-container width="423pt">
                        <fo:block border-bottom="0.6pt dotted black"
                                  th:text="${model.body.yearEnd}">&#160;</fo:block>
                    </fo:inline-container>
                </fo:block>
                <fo:block margin-left="7mm" space-before="3pt" font-size="9pt"
                          th:if="${model.body.term == 't2'}">Congratulations Your ward is Promoted to Class
                    <fo:inline-container width="200pt">
                        <fo:block border-bottom="0.6pt dotted black"
                                  th:text="${model.body.promotedClass}">&#160;</fo:block>
                    </fo:inline-container>
                </fo:block>
            </fo:block-container>
        </fo:flow>
    </fo:page-sequence>

    <!-- 4rt Page -->
    <fo:page-sequence master-reference="invoice">
        <fo:flow flow-name="xsl-region-body">
            <fo:block-container width="100%" height="100%" margin="0mm">

                <!-- Background Image -->
                <fo:block-container absolute-position="absolute" top="-40%" left="114%" width="0%" height="0%">
                    <fo:block text-align="center" color="white" font-size="48pt" font-family="Arial">
                        <fo:instream-foreign-object content-width="280%" content-height="280%">
                            <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="440%" height="440%" viewBox="0 0 100 100">
                                <image x="0" y="0" width="100%" height="100%" xlink:href="https://s3.ap-southeast-1.wasabisys.com/wexl-strapi-images/doon%20bharati%20report%20card/Nursery%20to%20UKG.jpg"/>
                            </svg> </fo:instream-foreign-object>
                    </fo:block>
                </fo:block-container>

                <!-- Title -->
                <fo:block-container absolute-position="absolute" left="36mm" top="-7mm"
                                    width="100mm" height="18mm" text-align="center">
                    <fo:block>
                        <fo:instream-foreign-object>
                            <svg xmlns="http://www.w3.org/2000/svg" width="100mm" height="18mm" viewBox="0 0 1000 240">
                                <!-- Wider pill with rounded corners -->
                                <rect x="0" y="0" width="1000" height="180" rx="90" ry="90" fill="#15113d"/>
                                <!-- Text with breathing space -->
                                <text x="50%" y="50%"
                                      dominant-baseline="middle" text-anchor="middle"
                                      font-family="Times New Roman" font-size="100" font-weight="bold" fill="white">
                                    INFORMATION
                                </text>
                            </svg>
                        </fo:instream-foreign-object>
                    </fo:block>
                </fo:block-container>

                <!-- Information Text -->
                <fo:block-container absolute-position="absolute" top="15mm" width="175mm">
                    <fo:list-block provisional-distance-between-starts="6mm" provisional-label-separation="3mm">

                        <fo:list-item space-after="2mm">
                            <fo:list-item-label end-indent="label-end()">
                                <fo:block font-size="11pt" font-weight="bold">•</fo:block>
                            </fo:list-item-label>
                            <fo:list-item-body start-indent="body-start()">
                                <fo:block font-size="11pt" font-weight="bold">
                                    This Learning Voyage is a continuous &amp; comprehensive assessment of child
                                    throughout the year in scholastic &amp; co-scholastic field.
                                </fo:block>
                            </fo:list-item-body>
                        </fo:list-item>

                        <fo:list-item space-after="2mm">
                            <fo:list-item-label end-indent="label-end()">
                                <fo:block font-size="11pt" font-weight="bold">•</fo:block>
                            </fo:list-item-label>
                            <fo:list-item-body start-indent="body-start()">
                                <fo:block font-size="11pt" font-weight="bold">
                                    We at BDPS, believe in preparing students to succeed in life. Discipline, punctuality
                                    &amp; smart hard work are the pillars of success attained through passion, commitment
                                    &amp; dedication.
                                </fo:block>
                            </fo:list-item-body>
                        </fo:list-item>

                        <fo:list-item space-after="2mm">
                            <fo:list-item-label end-indent="label-end()">
                                <fo:block font-size="11pt" font-weight="bold" >•</fo:block>
                            </fo:list-item-label>
                            <fo:list-item-body start-indent="body-start()">
                                <fo:block font-size="11pt" font-weight="bold">
                                    This Learning Voyage is an important document related to your ward &amp; it is required
                                    to be sent back to school within two working days of its receipt.
                                </fo:block>
                            </fo:list-item-body>
                        </fo:list-item>

                        <fo:list-item space-after="2pt">
                            <fo:list-item-label end-indent="label-end()">
                                <fo:block font-size="11pt" font-weight="bold">•</fo:block>
                            </fo:list-item-label>
                            <fo:list-item-body start-indent="body-start()">
                                <fo:block font-size="11pt" font-weight="bold">
                                    Scholastic areas are judged in 5 point grading system i.e A+, A, B+, B, B-
                                </fo:block>
                            </fo:list-item-body>
                        </fo:list-item>
                    </fo:list-block>
                </fo:block-container>

                <!-- Grading Table -->
                <fo:block-container absolute-position="absolute" top="61mm" left="13mm" width="110mm" font-size="11pt">
                    <fo:table table-layout="fixed" border="1pt solid black" width="100%">
                        <fo:table-column column-width="50mm"/>
                        <fo:table-column column-width="50mm"/>
                        <fo:table-column column-width="50mm"/>

                        <fo:table-header>
                            <fo:table-row>
                                <fo:table-cell border="1pt solid black">
                                    <fo:block text-align="center" font-weight="bold">Grade</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black">
                                    <fo:block text-align="center" font-weight="bold">Out of 20</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black">
                                    <fo:block text-align="center" font-weight="bold">Out of 100</fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-header>

                        <fo:table-body>
                            <fo:table-row>
                                <fo:table-cell border="1pt solid black" text-align="center" font-weight="bold"><fo:block>A+</fo:block></fo:table-cell>
                                <fo:table-cell border="1pt solid black" text-align="center" font-weight="bold"><fo:block>16-20</fo:block></fo:table-cell>
                                <fo:table-cell border="1pt solid black" text-align="center" font-weight="bold"><fo:block>46-50</fo:block></fo:table-cell>
                            </fo:table-row>
                            <fo:table-row>
                                <fo:table-cell border="1pt solid black" text-align="center" font-weight="bold"><fo:block>A</fo:block></fo:table-cell>
                                <fo:table-cell border="1pt solid black" text-align="center" font-weight="bold"><fo:block>11-15</fo:block></fo:table-cell>
                                <fo:table-cell border="1pt solid black" text-align="center" font-weight="bold"><fo:block>41-45</fo:block></fo:table-cell>
                            </fo:table-row>
                            <fo:table-row>
                                <fo:table-cell border="1pt solid black" text-align="center" font-weight="bold"><fo:block>B+</fo:block></fo:table-cell>
                                <fo:table-cell border="1pt solid black" text-align="center" font-weight="bold"><fo:block>6-10</fo:block></fo:table-cell>
                                <fo:table-cell border="1pt solid black" text-align="center" font-weight="bold"><fo:block>36-40</fo:block></fo:table-cell>
                            </fo:table-row>
                            <fo:table-row>
                                <fo:table-cell border="1pt solid black" text-align="center" font-weight="bold"><fo:block>B</fo:block></fo:table-cell>
                                <fo:table-cell border="1pt solid black" text-align="center" font-weight="bold"><fo:block>3-5</fo:block></fo:table-cell>
                                <fo:table-cell border="1pt solid black" text-align="center" font-weight="bold"><fo:block>31-35</fo:block></fo:table-cell>
                            </fo:table-row>
                            <fo:table-row>
                                <fo:table-cell border="1pt solid black" text-align="center" font-weight="bold"><fo:block>B-</fo:block></fo:table-cell>
                                <fo:table-cell border="1pt solid black" text-align="center" font-weight="bold"><fo:block>&lt;2 or Absent</fo:block></fo:table-cell>
                                <fo:table-cell border="1pt solid black" text-align="center" font-weight="bold"><fo:block>&lt;30 or Absent</fo:block></fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>
                </fo:block-container>

                <!-- More Information -->
                <fo:block-container absolute-position="absolute" top="95mm" width="175mm">
                    <fo:list-block provisional-distance-between-starts="6mm" provisional-label-separation="3mm">

                        <fo:list-item space-after="2mm">
                            <fo:list-item-label end-indent="label-end()">
                                <fo:block font-size="11pt" font-weight="bold">•</fo:block>
                            </fo:list-item-label>
                            <fo:list-item-body start-indent="body-start()">
                                <fo:block font-size="11pt" font-weight="bold" space-after="2mm">
                                    Co-scholastic areas are also assessed under 5 point grading system as above.
                                </fo:block>
                            </fo:list-item-body>
                        </fo:list-item>

                        <fo:list-item>
                            <fo:list-item-label end-indent="label-end()">
                                <fo:block font-size="11pt" font-weight="bold">•</fo:block>
                            </fo:list-item-label>
                            <fo:list-item-body start-indent="body-start()">
                                <fo:block font-size="11pt" font-weight="bold" space-after="2mm">
                                    At BDPS, we work at grass-root level through our Connecting the Roots Program which encircles
                                    learning of Music, Dance, ABACUS, Art &amp; Craft, Drawing, Meditation, Vedik Shiksha,
                                    Sports, Games, Projects, Practical experiences, Labs, various digital &amp; 5 senses oriented
                                    activities in academic &amp; co-curricular field.
                                </fo:block>
                            </fo:list-item-body>
                        </fo:list-item>
                    </fo:list-block>
                </fo:block-container>

                <!-- Signatures -->
                <fo:block-container absolute-position="absolute" left="-5mm" top="140mm" width="70mm">
                    <fo:block font-size="10pt" text-align="center" font-weight="bold">
                        <fo:leader leader-pattern="dots" leader-length="55mm"/>
                        <fo:block>
                            Specimen Signature of Mother
                        </fo:block>
                    </fo:block>
                </fo:block-container>

                <fo:block-container absolute-position="absolute" top="140mm" left="100mm" width="70mm">
                    <fo:block font-size="10pt" text-align="center" font-weight="bold">
                        <fo:leader leader-pattern="dots" leader-length="75mm"/>
                        <fo:block>
                            Specimen Signature of Father / Guardian
                        </fo:block>
                    </fo:block>
                </fo:block-container>

                <fo:block-container absolute-position="absolute" top="240mm" left="20mm" width="80mm">
                    <fo:block text-align="center" font-size="16pt" font-weight="bold" color="white"
                              padding="2mm">CONTACT NO.: 9533 000 123</fo:block>
                </fo:block-container>
            </fo:block-container>
        </fo:flow>
    </fo:page-sequence>

</fo:root>