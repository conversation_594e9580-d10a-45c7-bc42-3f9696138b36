package com.wexl.retail.commons.stories.dto;

import java.util.List;
import java.util.Map;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Component
@ConfigurationProperties(prefix = "story-ladder")
public class StoriesProperties {
  private Map<String, List<StoriesDto.Response>> types;

  public Map<String, List<StoriesDto.Response>> getTypes() {
    return types;
  }

  public void setTypes(Map<String, List<StoriesDto.Response>> types) {
    this.types = types;
  }
}
