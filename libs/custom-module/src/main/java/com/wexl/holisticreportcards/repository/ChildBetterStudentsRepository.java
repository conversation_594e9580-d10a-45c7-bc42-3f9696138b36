package com.wexl.holisticreportcards.repository;

import com.wexl.holisticreportcards.model.ChildBetterStudents;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface ChildBetterStudentsRepository extends JpaRepository<ChildBetterStudents, Long> {

  Optional<ChildBetterStudents> findByStudentId(Long studentId);
}
