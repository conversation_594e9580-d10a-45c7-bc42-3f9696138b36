package com.wexl.holisticreportcards.model;

import com.wexl.retail.model.Model;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Entity
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Table(name = "gillco_student_child_support")
public class StudentChildSupport extends Model {
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;

  @Column(name = "student_id")
  private Long studentId;

  @ManyToOne(fetch = FetchType.LAZY, cascade = CascadeType.ALL)
  @JoinColumn(name = "gillco_child_support_id")
  private ChildSupport childSupport;

  @Column(name = "value")
  private Boolean value;
}
