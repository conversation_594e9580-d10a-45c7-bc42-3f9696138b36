package com.wexl.holisticreportcards.repository;

import com.wexl.holisticreportcards.model.ChildBetter;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface ChildBetterRepository extends JpaRepository<ChildBetter, Long> {

  List<ChildBetter> findByOrgSlugAndGradeSlug(String orgSlug, String gradeSlug);
}
