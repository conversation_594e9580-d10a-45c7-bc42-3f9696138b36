package com.wexl.holisticreportcards.repository;

import com.wexl.holisticreportcards.model.SummarySheet;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface SummarySheetRepository extends JpaRepository<SummarySheet, Long> {

  List<SummarySheet> findByOrgSlugAndGradeSlug(String orgSlug, String gradeSlug);
}
