package com.wexl.holisticreportcards.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.wexl.gilico.dto.*;
import com.wexl.holisticreportcards.model.*;
import com.wexl.retail.subjects.model.SubjectsCategoryEnum;
import java.util.List;
import lombok.Builder;

public record ProgressCardDto() {

  @Builder
  public record Request(
      @JsonProperty("pallavi_primary_progress_card_id") Long PallaviPrimaryProgressCardId,
      @JsonProperty("student_id") Long studentId,
      String gradeSlug,
      String gradeName,
      @JsonProperty("mother_phone_no") String motherPhoneNo,
      @JsonProperty("address") String address,
      @JsonProperty("thing_i_like") String thingsILike,
      @JsonProperty("i_live_in") String iLiveIn,
      @JsonProperty("my_friends_are") String myFriendsAre,
      @JsonProperty("my_favourite_colours_are") String myFavouriteColoursAre,
      @JsonProperty("my_favourite_foods") String myFavouriteFoods,
      @JsonProperty("my_favourite_games") String myFavouriteGames,
      @JsonProperty("my_favourite_animals") String myFavouriteAnimals,
      @JsonProperty("a_glimpse_of_myself") String aGlimpseOfMySelf,
      @JsonProperty("a_glimpse_of_myfamily") String aGlimpseOfMyFamily,
      @JsonProperty("learners_portfolio") String learnersPortFolio,
      @JsonProperty("flower") String flower,
      @JsonProperty("aim") String aim,
      @JsonProperty("subject") String subject,
      @JsonProperty("house") String house,
      @JsonProperty("term1_height") String term1Height,
      @JsonProperty("term2_height") String term2Height,
      @JsonProperty("term1_weight") String term1Weight,
      @JsonProperty("term2_weight") String term2Weight,
      @JsonProperty("term1_remarks") String term1Remarks,
      @JsonProperty("term2_remarks") String term2Remarks,
      @JsonProperty("age") Long age,
      @JsonProperty("blood_group") String bloodGroup,
      @JsonProperty("dental") String dental,
      @JsonProperty("eyesight_l") String eyesightL,
      @JsonProperty("eyesight_r") String eyesightR,
      @JsonProperty("competencies") List<Competencies> competenciesList,
      @JsonProperty("self_assessments") List<SelfAssessment> selfAssessments,
      @JsonProperty("peer_assessments") List<PeerAssessment> peerAssessments,
      @JsonProperty("parents_feedback") List<ParentsFeedback> parentsFeedbacks,
      @JsonProperty("interested_activities") InterestedActivities interestedActivities,
      @JsonProperty("child_support") ChildSupport childSupport,
      @JsonProperty("parent_facilitator") parentFacilitator parentFacilitator,
      @JsonProperty("facilitator") Facilitator facilitator,
      @JsonProperty("summary_sheet") List<SummarySheet> summarySheet,
      @JsonProperty("child_better") List<ChildBetter> childBetter) {}

  @Builder
  public record Response(
      @JsonProperty("pallavi_primary_progress_card_id") Long PallaviPrimaryProgressCardId,
      @JsonProperty("student_id") Long studentId,
      @JsonProperty("student_name") String studentName,
      @JsonProperty("admission_no") String admissionNo,
      @JsonProperty("date_of_birth") Long dateOfBirth,
      @JsonProperty("dob") String dob,
      @JsonProperty("class_teacher") String classTeacher,
      @JsonProperty("father_name") String fatherName,
      @JsonProperty("mother_name") String motherName,
      @JsonProperty("father_phone_no") String fatherPhoneNo,
      @JsonProperty("mother_phone_no") String motherPhoneNo,
      @JsonProperty("grade_slug") String gradeSlug,
      @JsonProperty("grade_name") String gradeName,
      @JsonProperty("section_name") String sectionName,
      @JsonProperty("address") String address,
      @JsonProperty("thing_i_like") String thingsILike,
      @JsonProperty("i_live_in") String iLiveIn,
      @JsonProperty("my_friends_are") String myFriendsAre,
      @JsonProperty("my_favourite_colours_are") String myFavouriteColoursAre,
      @JsonProperty("my_favourite_foods") String myFavouriteFoods,
      @JsonProperty("flower") String flower,
      @JsonProperty("aim") String aim,
      @JsonProperty("subject") String subject,
      @JsonProperty("house") String house,
      @JsonProperty("my_favourite_games") String myFavouriteGames,
      @JsonProperty("my_favourite_animals") String myFavouriteAnimals,
      @JsonProperty("a_glimpse_of_myself") String aGlimpseOfMySelf,
      @JsonProperty("a_glimpse_of_myfamily") String aGlimpseOfMyFamily,
      @JsonProperty("a_glimpse_of_myself_path") String aGlimpseOfMySelfPath,
      @JsonProperty("a_glimpse_of_myfamily_path") String aGlimpseOfMyFamilyPath,
      @JsonProperty("learners_portfolio_path") String learnersPortFolioPath,
      @JsonProperty("learners_portfolio") String learnersPortFolio,
      @JsonProperty("term1_height") String term1Height,
      @JsonProperty("term2_height") String term2Height,
      @JsonProperty("term1_weight") String term1Weight,
      @JsonProperty("term2_weight") String term2Weight,
      @JsonProperty("blood_group") String bloodGroup,
      @JsonProperty("dental") String dental,
      @JsonProperty("eyesight_l") String eyesightL,
      @JsonProperty("eyesight_r") String eyesightR,
      @JsonProperty("competencies") List<Competencies> competenciesList,
      @JsonProperty("self_assessments") List<SelfAssessment> selfAssessments,
      @JsonProperty("peer_assessments") List<PeerAssessment> peerAssessment,
      @JsonProperty("parents_feedback") List<ParentsFeedback> parentsFeedbacks,
      @JsonProperty("interested_activities") InterestedActivities interestedActivities,
      @JsonProperty("child_support") ChildSupport childSupports,
      @JsonProperty("parent_facilitator") parentFacilitator parentFacilitator,
      @JsonProperty("facilitator") Facilitator facilitator,
      @JsonProperty("summary_sheet") List<SummarySheet> summarySheet,
      @JsonProperty("child_better") List<ChildBetter> childBetter) {}

  @Builder
  public record ChildSupport(
      @JsonProperty("subject_area") String anyOtherSubjectArea,
      @JsonProperty("child_support_details") List<ChildSupportDetails> childSupportDetails) {}

  @Builder
  public record ChildSupportDetails(Long id, String name, Boolean value) {}

  @Builder
  public record Competencies(
      Long id,
      @JsonProperty("subject_name") String subjectName,
      @JsonProperty("subject_slug") String subjectSlug,
      @JsonProperty("category") SubjectsCategoryEnum category,
      @JsonProperty("details") List<Skill> skills) {}

  @Builder
  public record Skill(
      @JsonProperty("skill_name") String skillName, List<Details> competencyDetails) {}

  @Builder
  public record Details(Long id, String name, CompetencyTypes term1, CompetencyTypes term2) {}

  @Builder
  public record SelfAssessment(
      Long id,
      String name,
      String term1,
      String term2,
      @JsonProperty("description_name") String descriptionName) {}

  @Builder
  public record PeerAssessment(
      Long id,
      String name,
      PeerAssessmentTypes term1,
      PeerAssessmentTypes term2,
      @JsonProperty("description_name") String descriptionName) {}

  @Builder
  public record ParentsFeedback(Long id, String name, String term1, String term2) {}

  @Builder
  public record ProgressCardResponse(
      @JsonProperty("pallavi_primary_progress_card_id") Long PallaviPrimaryProgressCardId) {}

  @Builder
  public record HolisticReportRequest(
      @JsonProperty("grade_slug") String gradeSlug,
      @JsonProperty("section_uuid") String sectionUuid) {}

  @Builder
  public record SummarySheet(
      Long id,
      String name,
      SummaryDescriptorTypes term1,
      SummaryDescriptorTypes term2,
      String skill) {}

  @Builder
  public record ChildBetter(Long id, String name, ChildBetterTypes term1, ChildBetterTypes term2) {}

  @Builder
  public record Skills(
      @JsonProperty("skill_name") String skillName, List<FacilitatorDetails> facilitatorDetails) {}

  @Builder
  public record FacilitatorDetails(
      Long id, String name, FacilitatorTypes term1, FacilitatorTypes term2) {}

  @Builder
  public record Facilitator(
      @JsonProperty("details") List<Skills> skills,
      @JsonProperty("area_of_strength") AreaofStrength areaOfStrength,
      @JsonProperty("barrier_of_success") BarrierofSuccess barrierOfSuccess,
      @JsonProperty("student_progress") String studentProgress,
      @JsonProperty("future_steps") String futureSteps,
      @JsonProperty("participation") String participation,
      @JsonProperty("achievement") String achievement,
      @JsonProperty("class_facilitator_remarks") String classFacilitatorRemarks,
      @JsonProperty("principal_remarks") String principalRemarks,
      @JsonProperty("thematic_concepts_term1") String thematicConceptsTerm1,
      @JsonProperty("thematic_concepts_term2") String thematicConceptsTerm2) {}

  public record AreaofStrength(
      @JsonProperty("take_feedback_positively") boolean takeFeedbackPositively,
      @JsonProperty("work_independently") boolean workIndependently,
      @JsonProperty("effective_communication") boolean effectiveCommunication,
      @JsonProperty("solutions_focused_thinking") boolean solutionsFocusedThinking,
      @JsonProperty("empathetic") boolean empathetic,
      @JsonProperty("organization_and_prioritization") boolean organizationAndPrioritization,
      @JsonProperty("collaborative_skills") boolean collaborativeSkills,
      @JsonProperty("responsible") boolean responsible,
      @JsonProperty("creative") boolean creative,
      @JsonProperty("concentration") boolean concentration,
      @JsonProperty("any_other") boolean anyOther,
      @JsonProperty("any_other_value") String otherValue) {}

  public record BarrierofSuccess(
      @JsonProperty("lack_of_attention") boolean lackOfAttention,
      @JsonProperty("lack_of_motivation") boolean lackOfMotivation,
      @JsonProperty("lack_of_preparation") boolean lackOfPreparation,
      @JsonProperty("peer_pressure") boolean peerPressure,
      @JsonProperty("undefined_goals") boolean undefinedGoals,
      @JsonProperty("domestic_issues") boolean domesticIssues,
      @JsonProperty("inappropriate_behaviour_in_class_room")
          boolean inappropriateBehaviourInClassRoom,
      @JsonProperty("severe_illness_of_injury") boolean severeIllnessOfInjury,
      @JsonProperty("none") boolean none,
      @JsonProperty("any_other") boolean anyOther,
      @JsonProperty("any_other_value") String otherValue) {}
}
