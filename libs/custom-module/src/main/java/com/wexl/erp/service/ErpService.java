package com.wexl.erp.service;

import com.wexl.dps.model.ErpIntegration;
import com.wexl.dps.repository.ErpIntegrationRepository;
import com.wexl.erp.dto.ErpDto;
import com.wexl.retail.organization.admin.StudentResponse;
import com.wexl.retail.organization.model.Organization;
import com.wexl.retail.organization.repository.OrganizationRepository;
import com.wexl.retail.repository.StudentRepository;
import com.wexl.retail.repository.UserRepository;
import jakarta.transaction.Transactional;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.stereotype.Service;
import org.thymeleaf.TemplateEngine;
import org.thymeleaf.context.Context;

@Service
@RequiredArgsConstructor
@Slf4j
public class ErpService {

  private final ErpTeacherProcessor erpTeacherProcessor;
  private final ErpStudentProcessor erpStudentProcessor;
  private final ErpIntegrationRepository erpIntegrationRepository;
  private final ErpFeeStudentProcessor erpFeeProcessor;
  private final TemplateEngine templateEngine;
  private final JavaMailSender emailSender;
  private final StudentRepository studentRepository;
  private final OrganizationRepository organizationRepository;
  private final UserRepository userRepository;

  @Value("${spring.mail.from-email}")
  private String fromEmail;

  private static final String MAIL_CONTENT_TEMPLATE = "content";

  @Transactional
  public ErpDto.Response syncErpProcess(ErpDto.ErpEntityChangeResponse response) {
    log.info("Processing response: {}", response);

    AtomicInteger addStudent = new AtomicInteger();
    AtomicInteger updateStudent = new AtomicInteger();
    AtomicInteger deleteStudent = new AtomicInteger();

    List<ErpDto.StudentProcessResponse> studentResponses = new ArrayList<>();

    response
        .erpEntityChanges()
        .forEach(
            request -> {
              if ("teacher".equals(request.type())) {
                erpTeacherProcessor.process(request);
              } else if ("student".equals(request.type())) {

                switch (request.changeType()) {
                  case "ADD" -> addStudent.getAndIncrement();
                  case "UPDATE" -> updateStudent.getAndIncrement();
                  case "DELETE" -> deleteStudent.getAndIncrement();
                  default -> log.error("student change Type not found");
                }
                var studentResponse = erpStudentProcessor.process(request);
                studentResponses.addAll(
                    buildStudentResponse(studentResponse, request.changeType()));
              } else if ("fee".equals(request.type())) {
                switch (request.changeType()) {
                  case "UPDATE" -> updateStudent.getAndIncrement();
                  default -> log.error("student change Type not found");
                }
                StudentResponse feeStudentResponses =
                    erpFeeProcessor.process(request, response.admissionNumbers());
                studentResponses.addAll(
                    buildStudentResponse(feeStudentResponses, request.changeType()));
              } else {
                log.info("Do not understand type.");
              }
            });

    erpIntegrationRepository.save(
        ErpIntegration.builder()
            .type(response.erpEntityChanges().getFirst().type())
            .lastSyncedAt(LocalDateTime.now())
            .json(String.valueOf(response))
            .build());

    return ErpDto.Response.builder()
        .addStudent(addStudent.get())
        .updateStudent(updateStudent.get())
        .deleteStudent(deleteStudent.get())
        .studentResponses(studentResponses)
        .type(response.erpEntityChanges().getFirst().type())
        .build();
  }

  private List<ErpDto.StudentProcessResponse> buildStudentResponse(
      StudentResponse studentResponse, String changeType) {

    List<ErpDto.StudentProcessResponse> studentResponses = new ArrayList<>();

    if (studentResponse.isEmpty() || studentResponses == null) {
      studentResponses.add(
          ErpDto.StudentProcessResponse.builder()
              .name(studentResponse != null ? studentResponse.getMessage() : "N/A")
              .section("N/A")
              .grade("N/A")
              .board("N/A")
              .changeType(changeType)
              .dueAmount(studentResponse.getFeeDueAmount())
              .build());
    } else {
      String name =
          safe(studentResponse.getFirstName()) + " " + safe(studentResponse.getLastName());
      String section = safe(studentResponse.getSection());
      String grade = safe(studentResponse.getGradeSlug());
      String board = safe(studentResponse.getBoardSlug());

      studentResponses.add(
          ErpDto.StudentProcessResponse.builder()
              .name(name)
              .section(section)
              .grade(grade)
              .board(board)
              .changeType(changeType)
              .dueAmount(studentResponse.getFeeDueAmount())
              .build());
    }
    return studentResponses;
  }

  public void sendStudentNotification(ErpDto.Response response, String orgSlug) {

    List<String> orgSlugList = new ArrayList<>();
    orgSlugList.add(orgSlug);

    Organization organization = organizationRepository.findBySlug(orgSlug);
    var activeStudents = userRepository.getStudentsCountOfOrg(orgSlugList);
    var inactiveStudents = studentRepository.findByOrgSlugAndDeletedAtIsNotNull(orgSlug);

    var updatedResponse =
        ErpDto.Response.builder()
            .addStudent(response.addStudent())
            .updateStudent(response.updateStudent())
            .deleteStudent(response.deleteStudent())
            .unchangedStudents(response.unchangedStudents())
            .activeStudents(activeStudents)
            .inActiveStudents(inactiveStudents.size())
            .totalActiveInActiveStudents(activeStudents + inactiveStudents.size())
            .totalStudents(response.totalStudents())
            .type(response.type())
            .studentResponses(response.studentResponses())
            .sectionChangedStudents(response.sectionChangedStudents())
            .build();

    Map<String, String> recipientDetails = new HashMap<>();
    recipientDetails.put("Gowtham", "<EMAIL>");
    recipientDetails.put("Roja", "<EMAIL>");
    recipientDetails.put("Sai Sk", "<EMAIL>");

    sendStudentDetailsEmailNotification(updatedResponse, organization, recipientDetails);
  }

  public void sendStudentDetailsEmailNotification(
      ErpDto.Response studentSummary,
      Organization organization,
      Map<String, String> recipientDetails) {
    String organizationName = organization.getName();
    JavaMailSender sender = emailSender;
    String from = fromEmail;

    for (Map.Entry<String, String> entry : recipientDetails.entrySet()) {
      String recipientName = entry.getKey();
      String recipientEmail = entry.getValue();
      String type = studentSummary.type();
      try {

        // Prepare template model
        Map<String, Object> model = new HashMap<>();
        model.put("name", recipientName);
        model.put("organizationName", organizationName);

        String content = buildStudentContent(organizationName, studentSummary);
        String feeContent = buildFeeContent(organizationName, studentSummary);

        if (type.equals("fee")) {
          model.put(MAIL_CONTENT_TEMPLATE, feeContent);
        } else {
          model.put(MAIL_CONTENT_TEMPLATE, content);
        }

        var message = sender.createMimeMessage();
        var helper =
            new MimeMessageHelper(
                message,
                MimeMessageHelper.MULTIPART_MODE_MIXED_RELATED,
                StandardCharsets.UTF_8.name());

        helper.setTo(recipientEmail);
        helper.setSubject(
            type.equals("fee")
                ? organizationName + " - Fee Sync Summary Report"
                : organizationName + " - Student Sync Summary Report 📋");
        helper.setFrom(from);

        var context = new Context();
        context.setVariables(model);
        String html = templateEngine.process("student-counts-email", context);

        helper.setText(html, true);

        log.info("Sending student sync summary email to: {}", recipientEmail);
        sender.send(message);
        log.info("Student summary email sent!");

      } catch (Exception e) {
        log.error("Error sending student summary email", e);
      }
    }
  }

  private String buildFeeContent(String organizationName, ErpDto.Response studentSummary) {
    String content =
            """
                     <p>Please find below the summary of fee data synced today for the organization: <strong>%s</strong>.</p>

                     <ul>
                         <li><strong>Total Active students (ERP) - </strong> %d</li>
                         <li><strong>Total Active Student (LMS) - </strong> %d</li>
                         <li><strong>Students Added In LMS - </strong> %d</li>
                         <li><strong>Students Updated In LMS - </strong> %d</li>
                         <li><strong>Students Deleted In LMS - </strong> %d</li>
                         <li><strong>Unchanged Students From ERP - </strong> %d</li>
                         <li><strong>Inactive Students (LMS) - </strong> %d</li>
                         <li><strong>Total Active & Inactive students (LMS) - </strong> %d</li>
                     </ul>

                     """
            .formatted(
                organizationName,
                studentSummary.totalStudents(),
                studentSummary.activeStudents(),
                studentSummary.addStudent(),
                studentSummary.updateStudent(),
                studentSummary.deleteStudent(),
                studentSummary.unchangedStudents(),
                studentSummary.inActiveStudents(),
                studentSummary.totalActiveInActiveStudents());

    String feeStudentTableHtml = buildFeeStudentTableHtml(studentSummary.studentResponses());
    content += feeStudentTableHtml;
    return content;
  }

  private String buildStudentContent(String organizationName, ErpDto.Response studentSummary) {

    String content =
            """

                     <p>Please find below the summary of student data synced today for the organization: <strong>%s</strong>.</p>

                     <ul>
                         <li><strong>Total Active Students (ERP) - </strong> %d</li>
                         <li><strong>Total Active Students (LMS) - </strong> %d</li>
                         <li><strong>Students Added In LMS - </strong> %d</li>
                         <li><strong>Students Updated In LMS - </strong> %d</li>
                         <li><strong>Students Deleted In LMS - </strong> %d</li>
                         <li><strong>Unchanged Students From ERP - </strong> %d</li>
                         <li><strong>Inactive Students (LMS) - </strong> %d</li>
                         <li><strong>Total Active & Inactive students (LMS) - </strong> %d</li>
                     </ul>

                     """
            .formatted(
                organizationName,
                studentSummary.totalStudents(),
                studentSummary.activeStudents(),
                studentSummary.addStudent(),
                studentSummary.updateStudent(),
                studentSummary.deleteStudent(),
                studentSummary.unchangedStudents(),
                studentSummary.inActiveStudents(),
                studentSummary.totalActiveInActiveStudents());

    String studentTableHtml = buildStudentTableHtml(studentSummary.studentResponses());
    content += studentTableHtml;

    if (studentSummary.sectionChangedStudents() != null
        && !studentSummary.sectionChangedStudents().isEmpty()) {
      content +=
          "<br><br>"
              + buildSectionChangeTableHtml(
                  studentSummary.sectionChangedStudents(), organizationName);
    } else {
      content += "<br><br><p>No section changes reported today.</p>";
    }

    return content;
  }

  private String buildSectionChangeTableHtml(
      List<ErpDto.SectionChangedStudents> sectionChangedStudents, String organizationName) {
    StringBuilder tableBuilder = new StringBuilder();

    tableBuilder.append(
            """
            <p>The following students have had their section updated in <strong>%s</strong>.</p>
            <h3>Section Changed Students</h3>
            <table border='1' cellspacing='0' cellpadding='5'
                   style='border-collapse: collapse; width: 100%%; font-family: Arial, sans-serif; font-size: 14px;'>
              <thead style='background-color: #f2f2f2;'>
                <tr>
                  <th>S.No</th>
                  <th>Student Code</th>
                  <th>Name</th>
                  <th>Wexl Section</th>
                  <th>Erp Section</th>
                </tr>
              </thead>
              <tbody>
            """
            .formatted(safe(organizationName)));

    int serialNumber = 1;
    for (ErpDto.SectionChangedStudents student : sectionChangedStudents) {
      tableBuilder.append(
              """
              <tr>
                <td>%d</td>
                <td>%s</td>
                <td>%s</td>
                <td>%s</td>
                <td>%s</td>
              </tr>
              """
              .formatted(
                  serialNumber++,
                  safe(student.studentCode()),
                  safe(student.name()),
                  safe(student.wexlSection()),
                  safe(student.erpSection())));
    }

    tableBuilder.append("</tbody></table>");
    return tableBuilder.toString();
  }

  private String buildFeeStudentTableHtml(List<ErpDto.StudentProcessResponse> feeStudentResponses) {
    if (feeStudentResponses == null || feeStudentResponses.isEmpty()) {
      return "<p>No fee student data available for detailed report.</p>";
    }
    StringBuilder tableBuilder = new StringBuilder();

    tableBuilder.append(
        """
          <h3>Detailed Fee Student Sync Report</h3>
          <table border='1' cellspacing='0' cellpadding='5'
                 style='border-collapse: collapse; width: 100%; font-family: Arial, sans-serif; font-size: 14px;'>
            <thead style='background-color: #f2f2f2;'>
              <tr>
                <th>S.No</th>
                <th>Name</th>
                <th>Section</th>
                <th>Grade</th>
                <th>Board</th>
                <th>Type</th>
                <th>Due Amount</th>
              </tr>
            </thead>
            <tbody>
      """);

    int serialNumber = 1;
    for (ErpDto.StudentProcessResponse response : feeStudentResponses) {
      tableBuilder.append(
              """
                <tr>
                  <td>%d</td>
                  <td>%s</td>
                  <td>%s</td>
                  <td>%s</td>
                  <td>%s</td>
                  <td>%s</td>
                  <td>%s</td>
                </tr>
              """
              .formatted(
                  serialNumber++,
                  safe(response.name()),
                  safe(response.section()),
                  safe(response.grade()),
                  safe(response.board()),
                  safe(response.changeType()),
                  response.dueAmount() != null ? response.dueAmount().toString() : "N/A"));
    }

    tableBuilder.append("</tbody></table>");
    return tableBuilder.toString();
  }

  private String buildStudentTableHtml(List<ErpDto.StudentProcessResponse> studentResponses) {
    if (studentResponses == null || studentResponses.isEmpty()) {
      return "<p>No student data available for detailed report.</p>";
    }

    StringBuilder tableBuilder = new StringBuilder();

    tableBuilder.append(
        """
      <h3>Detailed Student Sync Report</h3>
      <table border='1' cellspacing='0' cellpadding='5'
             style='border-collapse: collapse; width: 100%; font-family: Arial, sans-serif; font-size: 14px;'>
        <thead style='background-color: #f2f2f2;'>
          <tr>
            <th>S.No</th>
            <th>Name</th>
            <th>Section</th>
            <th>Grade</th>
            <th>Board</th>
            <th>Type</th>
          </tr>
        </thead>
        <tbody>
  """);

    int serialNumber = 1;
    for (ErpDto.StudentProcessResponse response : studentResponses) {
      tableBuilder.append(
              """
                <tr>
                  <td>%d</td>
                  <td>%s</td>
                  <td>%s</td>
                  <td>%s</td>
                  <td>%s</td>
                  <td>%s</td>
                </tr>
              """
              .formatted(
                  serialNumber++,
                  safe(response.name()),
                  safe(response.section()),
                  safe(response.grade()),
                  safe(response.board()),
                  safe(response.changeType())));
    }

    tableBuilder.append("</tbody></table>");
    return tableBuilder.toString();
  }

  private String safe(String value) {
    return value == null ? "" : value;
  }
}
