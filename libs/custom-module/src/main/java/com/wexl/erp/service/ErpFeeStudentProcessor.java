package com.wexl.erp.service;

import com.wexl.erp.dto.ErpDto;
import com.wexl.retail.organization.admin.StudentResponse;
import com.wexl.retail.repository.StudentRepository;
import com.wexl.retail.repository.UserRepository;
import com.wexl.retail.student.auth.StudentAuthTransformer;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
@Slf4j
public class ErpFeeStudentProcessor {
  private final StudentRepository studentRepository;
  private final UserRepository userRepository;
  private final StudentAuthTransformer studentAuthTransformer;

  public StudentResponse process(ErpDto.ErpEntityChange request, List<String> admissionNumbers) {
    StudentResponse studentResponse = null;
    if (request.changeType().equals("UPDATE")) {
      studentResponse = updateFee(request);
    } else {
      log.error("change Type not found");
      studentResponse =
          StudentResponse.builder()
              .message("Change type not found: " + request.changeType())
              .isEmpty(true)
              .build();
    }
    updateFeeStudent(admissionNumbers, request.feeResponse().orgSlug());
    return studentResponse;
  }

  private StudentResponse updateFee(ErpDto.ErpEntityChange request) {
    var externalRef = request.feeResponse().studentCode();
    String errorMessage = null;
    try {
      var possibleUser = userRepository.findByExternalRef(externalRef);
      if (possibleUser.isEmpty()) {
        return StudentResponse.builder()
            .message("User not found for external reference: " + externalRef)
            .isEmpty(true)
            .build();
      }
      var isStudentExists = studentRepository.findByUserInfo(possibleUser.get());
      var student = isStudentExists.get();
      student.setFeeDefaulter(!request.feeResponse().dueAmount().equals("0"));
      student.setFeeDueAmount(Long.parseLong(request.feeResponse().dueAmount()));
      var studentEntity = studentRepository.save(student);
      var section = studentEntity.getSection();
      return studentAuthTransformer.studentResponseFrom(
          studentEntity.getUserInfo(),
          section.getGradeName(),
          section.getBoardName(),
          student.getSchoolName(),
          student.getRollNumber(),
          student.getClassRollNumber(),
          student.getAcademicYearSlug(),
          section.getName(),
          "");
    } catch (Exception e) {
      log.error("Error in updating a student [{}]", e.getMessage());
      errorMessage =
          String.format(
              "Failed to updating a student with externalRef [%s]: %s",
              externalRef, e.getMessage() != null ? e.getMessage() : "Unknown error");
    }
    return StudentResponse.builder().message(errorMessage).isEmpty(true).build();
  }

  public void updateFeeStudent(List<String> admissionNumbers, String orgSlug) {
    {
      try {
        studentRepository.updateFeeStudent(admissionNumbers, orgSlug);
      } catch (Exception e) {
        log.error("Error in updating a students [{}]", e.getMessage());
      }
    }
  }
}
