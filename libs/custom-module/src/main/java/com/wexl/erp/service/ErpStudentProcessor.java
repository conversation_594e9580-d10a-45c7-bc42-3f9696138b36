package com.wexl.erp.service;

import static com.wexl.retail.section.domain.SectionStatus.ACTIVE;

import com.wexl.erp.dto.ErpDto;
import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.globalprofile.repository.RoleTemplateRepository;
import com.wexl.retail.guardian.dto.GuardianRequest;
import com.wexl.retail.guardian.model.GuardianRole;
import com.wexl.retail.guardian.service.GuardianService;
import com.wexl.retail.model.Gender;
import com.wexl.retail.organization.admin.StudentRequest;
import com.wexl.retail.organization.admin.StudentResponse;
import com.wexl.retail.organization.repository.OrganizationRepository;
import com.wexl.retail.repository.StudentRepository;
import com.wexl.retail.repository.UserRepository;
import com.wexl.retail.section.domain.Section;
import com.wexl.retail.section.dto.request.SectionCreateRequest;
import com.wexl.retail.section.repository.SectionRepository;
import com.wexl.retail.section.service.SectionService;
import com.wexl.retail.student.auth.StudentAuthService;
import com.wexl.retail.student.auth.StudentAuthTransformer;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@Slf4j
@RequiredArgsConstructor
public class ErpStudentProcessor {
  private final StudentAuthService studentAuthService;
  private final UserRepository userRepository;
  private final SectionRepository sectionRepository;
  private final StudentRepository studentRepository;
  private final RoleTemplateRepository roleTemplateRepository;
  private final OrganizationRepository organizationRepository;
  private final SectionService sectionService;
  private final GuardianService guardianService;
  private final StudentAuthTransformer studentAuthTransformer;
  private static final String STM = "stm802460";

  public StudentResponse process(ErpDto.ErpEntityChange request) {
    return switch (request.changeType()) {
      case "ADD" -> createStudent(request);
      case "UPDATE" -> updateStudent(request);
      case "DELETE" -> deleteStudent(request);
      default -> {
        log.error("change Type not found");
        yield null;
      }
    };
  }

  private StudentResponse updateStudent(ErpDto.ErpEntityChange request) {
    var externalRef = request.studentResponse().studentCode();
    String errorMessage = null;
    try {
      var possibleUser = userRepository.findByExternalRef(externalRef);
      if (possibleUser.isEmpty()) {
        return createStudent(request);
      }
      var isStudentExists = studentRepository.findByUserInfo(possibleUser.get());
      var student = isStudentExists.get();
      var guardians = guardianService.fetchGuardiansDetails(student.getUserInfo().getAuthUserId());
      StudentResponse studentResponse =
          studentAuthService.editStudent(
              student.getUserInfo().getOrganization(),
              student.getUserInfo().getAuthUserId(),
              buildStudentRequest(request));
      var guardianRequest = buildGuardians(request);
      if (guardians.isEmpty() && !guardianRequest.isEmpty()) {
        guardianService.createGuardian(student.getUserInfo().getAuthUserId(), guardianRequest);
      } else {
        guardians.forEach(
            guardian -> {
              var req =
                  guardianRequest.stream()
                      .filter(
                          guardianRequest1 ->
                              guardianRequest1.getRelationType().equals(guardian.relationType()))
                      .findFirst();
              guardianService.editGuardian(
                  req.get(), guardian.id(), student.getUserInfo().getAuthUserId());
            });
      }
      return studentResponse;
    } catch (Exception e) {
      log.error("Error in updating a student [{}]", e.getMessage());
      errorMessage = e.getMessage();
    }
    return StudentResponse.builder().message(errorMessage).isEmpty(true).build();
  }

  public StudentResponse createStudent(ErpDto.ErpEntityChange request) {
    var externalRef = request.studentResponse().studentCode();
    String errorMessage = null;
    try {
      var user = userRepository.findByExternalRef(externalRef);
      if (user.isEmpty()) {
        var newStudentRequest = buildStudentRequest(request);
        var student =
            studentAuthService.createStudent(
                newStudentRequest, request.studentResponse().orgSlug());
        if (!request.studentResponse().fatherName().isEmpty()
            || !request.studentResponse().motherName().isEmpty()) {
          guardianService.createGuardian(
              student.getUserInfo().getAuthUserId(), buildGuardians(request));
        }
        var section = student.getSection();
        var userDetails = student.getUserInfo();
        return studentAuthTransformer.studentResponseFrom(
            userDetails,
            section.getGradeName(),
            section.getBoardName(),
            student.getSchoolName(),
            student.getRollNumber(),
            student.getClassRollNumber(),
            student.getAcademicYearSlug(),
            section.getName(),
            "");
      } else {
        var userDetails = user.get();
        userDetails.setDeletedAt(null);
        userDetails.setIsDeleted(null);
        userRepository.save(userDetails);
        return updateStudent(request);
      }
    } catch (Exception e) {
      log.error("Error in creating a student [{}]", e.getMessage(), e);
      errorMessage =
          String.format(
              "Failed to create student with externalRef [%s]: %s",
              externalRef, e.getMessage() != null ? e.getMessage() : "Unknown error");
    }
    return StudentResponse.builder().message(errorMessage).isEmpty(true).build();
  }

  private StudentResponse deleteStudent(ErpDto.ErpEntityChange request) {
    var externalRef = request.employeeCode();
    String errorMessage = null;
    try {
      var possibleUser = userRepository.findByExternalRef(externalRef);
      if (possibleUser.isEmpty()) {
        return StudentResponse.builder()
            .message("User not found for external reference: " + externalRef)
            .isEmpty(true)
            .build();
      }
      var user = possibleUser.get();
      var isStudentExists = studentRepository.findByUserInfo(possibleUser.get());
      if (isStudentExists.isPresent()) {
        var student = isStudentExists.get();

        studentAuthService.deleteStudent(
            student.getUserInfo().getOrganization(), student.getUserInfo().getAuthUserId());

        var section = student.getSection();
        return studentAuthTransformer.studentResponseFrom(
            user,
            section != null ? section.getGradeName() : "N/A",
            section != null ? section.getBoardName() : "N/A",
            student.getSchoolName(),
            student.getRollNumber(),
            student.getClassRollNumber(),
            student.getAcademicYearSlug(),
            section != null ? section.getName() : "N/A",
            "");
      }
    } catch (Exception e) {
      log.error("Error in deleting a student [{}]", e.getMessage());
      errorMessage =
          String.format(
              "Failed to delete student with externalRef [%s]: %s",
              externalRef, e.getMessage() != null ? e.getMessage() : "Unknown error");
    }
    return StudentResponse.builder().message(errorMessage).build();
  }

  private StudentRequest buildStudentRequest(ErpDto.ErpEntityChange request) {
    var studentRequest = request.studentResponse();
    var roleTemplate = roleTemplateRepository.findBySlug("student-bet admission profile");
    var schoolName = organizationRepository.findBySlug(studentRequest.orgSlug()).getName();
    var possibleSection =
        sectionRepository.findByNameAndOrganization(
            Objects.isNull(studentRequest.sectionName()) ? "Z" : studentRequest.sectionName(),
            request.studentResponse().orgSlug());
    Section section;
    if (possibleSection.isEmpty()) {
      log.error("Section is not present: {}", studentRequest.sectionUuid());
      log.info("creating Section");
      section =
          sectionService.createSection(
              request.studentResponse().orgSlug(), buildSectionRequest(request));
      log.info("Section created");
    } else {
      section = possibleSection.get();
    }

    if (roleTemplate.isEmpty()) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.roleTemplate not found");
    }
    return StudentRequest.builder()
        .section(section.getName())
        .schoolName(schoolName)
        .boardSlug(section.getBoardSlug())
        .email(studentRequest.email())
        .rollNumber(studentRequest.rollNumber())
        .roleTemplate(roleTemplate.getFirst())
        .externalRef(studentRequest.studentCode())
        .lastName(studentRequest.lastName())
        .userName(
            STM.equals(studentRequest.orgSlug())
                ? "st" + studentRequest.rollNumber()
                : studentRequest.rollNumber())
        .firstName(studentRequest.firstName())
        .password("password@123")
        .gender(Gender.valueOf(studentRequest.gender()))
        .mobileNumber(studentRequest.phone())
        .academicYearSlug("25-26")
        .gradeSlug(section.getGradeSlug())
        .build();
  }

  private SectionCreateRequest buildSectionRequest(ErpDto.ErpEntityChange request) {
    var gradeSlug = request.studentResponse().grade();
    return SectionCreateRequest.builder()
        .name(request.studentResponse().sectionName())
        .gradeSlug(gradeSlug)
        .status(ACTIVE)
        .boardSlug("cbse")
        .build();
  }

  private List<GuardianRequest> buildGuardians(ErpDto.ErpEntityChange request) {
    var studentResponse = request.studentResponse();
    List<GuardianRequest> guardians = new ArrayList<>();
    guardians.add(
        GuardianRequest.builder()
            .firstName(studentResponse.fatherName())
            .lastName(studentResponse.lastName())
            .relationType(GuardianRole.FATHER)
            .emailId(studentResponse.fatherEmail())
            .isPrimary(true)
            .countryCodeId(1L)
            .mobileNumber(
                studentResponse.fatherPhone() != null
                    ? studentResponse.fatherPhone().replace("+91", "")
                    : "")
            .build());
    guardians.add(
        GuardianRequest.builder()
            .firstName(studentResponse.motherName())
            .lastName(studentResponse.lastName())
            .relationType(GuardianRole.MOTHER)
            .emailId(studentResponse.motherEmail())
            .isPrimary(false)
            .countryCodeId(1L)
            .mobileNumber(
                studentResponse.motherPhone() != null
                    ? studentResponse.motherPhone().replace("+91", "")
                    : "")
            .build());

    return guardians;
  }
}
