package com.wexl.dps.reportcard;

import static java.lang.String.format;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.wexl.dps.dto.SportsReportCardDto;
import com.wexl.dps.managereportcard.repository.ReportCardConfigDataRepository;
import com.wexl.dps.managereportcard.repository.ReportCardConfigRepository;
import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.metrics.reportcards.ReportCardService;
import com.wexl.retail.model.Student;
import com.wexl.retail.model.User;
import com.wexl.retail.offlinetest.dto.LowerGradeReportDto;
import com.wexl.retail.offlinetest.dto.LowerGradeReportDto.Marks;
import com.wexl.retail.offlinetest.dto.ReportCardDto;
import com.wexl.retail.offlinetest.model.ReportCardTemplate;
import com.wexl.retail.offlinetest.repository.LowerGradeReportCardData;
import com.wexl.retail.offlinetest.repository.OfflineTestScheduleStudentAttendanceRepository;
import com.wexl.retail.offlinetest.repository.OfflineTestScheduleStudentRepository;
import com.wexl.retail.offlinetest.service.OfflineTestScheduleService;
import com.wexl.retail.offlinetest.service.pointscale.PointScaleEvaluator;
import com.wexl.retail.organization.model.Organization;
import com.wexl.retail.reportcards.model.ReportCardConfigDetail;
import com.wexl.retail.student.attributes.model.StudentAttributeValueModel;
import com.wexl.retail.student.attributes.service.StudentAttributeService;
import com.wexl.retail.subjects.model.SubjectsCategoryEnum;
import com.wexl.retail.subjects.model.SubjectsTypeEnum;
import com.wexl.retail.term.model.TermAssessment;
import com.wexl.retail.term.repository.TermAssessmentRepository;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class LowerGradeFirstTermGradeAndMarksReportCard extends BaseReportCardDefinition {

  private final OfflineTestScheduleStudentRepository offlineTestScheduleStudentRepository;
  private final ReportCardService reportCardService;
  private final OfflineTestScheduleService offlineTestScheduleService;
  private final PointScaleEvaluator pointScaleEvaluator;
  private final OfflineTestScheduleStudentAttendanceRepository
      offlineTestScheduleStudentAttendanceRepository;
  private final ReportCardConfigDataRepository reportCardConfigDataRepository;
  private final TermAssessmentRepository termAssessmentRepository;
  private final ReportCardConfigRepository reportCardConfigRepository;
  private final StudentAttributeService studentAttributeService;
  private static final String DPS_AEROCITY_SLUG = "del189476";
  private final SportsReportCard sportsReportCard;
  private final LowerGradeOverallReportCard lowerGradeOverallReportCard;

  @Value("classpath:dps-grades-description.json")
  private Resource dpsGradesDescription;

  @Override
  public Map<String, Object> build(User user, Organization org, ReportCardDto.Request request) {
    var header = buildHeader(user.getStudentInfo(), org);
    var body = buildBody(user, org.getSlug(), request.withMarks(), request.termId());
    return Map.of("header", header, "pages", List.of(1), "body", body);
  }

  @Override
  public Map<String, Object> buildBody(User user, Organization org, ReportCardDto.Request request) {
    return null;
  }

  @Override
  public boolean supports(ReportCardTemplate reportCardTemplate) {
    final String config = reportCardTemplate.getConfig();
    return config.equals("lower-grade-term1-marks-grades-report.xml");
  }

  @Override
  public Optional<StudentAttributeValueModel> getStudentAttributeValue(
      Student student, String key) {
    return reportCardService.getStudentAttributeValue(student, "admission_no");
  }

  public LowerGradeReportDto.Body buildBody(
      User user, String orgSlug, Boolean withMarks, Long termId) {
    var student = user.getStudentInfo();
    Optional<StudentAttributeValueModel> mother =
        reportCardService.getStudentAttributeValue(student, "mother_name");
    Optional<StudentAttributeValueModel> father =
        reportCardService.getStudentAttributeValue(student, "father_name");
    Optional<StudentAttributeValueModel> dateOfBirth =
        reportCardService.getStudentAttributeValue(student, "date_of_birth");
    var tableMarks = buildTableMarks(student, withMarks, orgSlug);
    var testDefinition =
        offlineTestScheduleService.validateOfflineTestDefinition(
            tableMarks.firstTableMarks().getFirst().otdId());

    List<SportsReportCardDto.MainTable> physicalData =
        lowerGradeOverallReportCard.buildPhysicalData(user, termId);
    SportsReportCardDto.Overall physData =
        sportsReportCard.getLearningLevelByListOfGrades(physicalData);

    return LowerGradeReportDto.Body.builder()
        .name((user.getFirstName() + " " + user.getLastName()).toUpperCase())
        .dateOfBirth(studentAttributeService.getDateOfBirthFormat(dateOfBirth))
        .className(student.getSection().getName())
        .orgSlug(orgSlug)
        .gradeSlug(student.getSection().getGradeSlug())
        .rollNumber(student.getClassRollNumber())
        .mothersName(mother.map(StudentAttributeValueModel::getValue).orElse(null))
        .fathersName(father.map(StudentAttributeValueModel::getValue).orElse(null))
        .firstTable(
            buildFirstTable(
                tableMarks.firstTableMarks(), tableMarks.externalMarks(), student, withMarks))
        .secondTable(buildSecondTable(tableMarks.secondTableMarks()))
        .thirdTable(buildThirdTable(tableMarks.thirdTableMarks()))
        .fourthTable(buildFourthTable(physData))
        .attendance(buildAttendance(student.getId()))
        .gradeTable(LowerGradeReportDto.GradeTable.builder().title("Grade Scale").build())
        .gradingScale(
            testDefinition.getGradeScaleSlug() != null
                ? testDefinition.getGradeScaleSlug()
                : "4point")
        .build();
  }

  private List<LowerGradeReportDto.FourthTable> buildFourthTable(
      SportsReportCardDto.Overall physData) {
    return Collections.singletonList(
        LowerGradeReportDto.FourthTable.builder().term1Grade(physData.overallGrade()).build());
  }

  public LowerGradeReportDto.FirstTable buildFirstTable(
      List<Marks> firstTableMarks, List<Marks> externalMarks, Student student, Boolean withMarks) {
    var section = student.getSection();

    var reportCardConfigs =
        reportCardConfigRepository.findAllByOrgSlugAndBoardSlugAndGradeSlug(
            section.getOrganization(), section.getBoardSlug(), section.getGradeSlug());
    var configDetails =
        reportCardConfigs.getFirst().getReportCardConfigDetails().stream()
            .sorted(Comparator.comparing(ReportCardConfigDetail::getSeqNo))
            .toList();

    var total =
        configDetails.stream()
            .filter(x -> x.getWeightage() != null)
            .mapToLong(ReportCardConfigDetail::getWeightage)
            .sum();
    List<String> subjectGradeSlugs =
        firstTableMarks.stream()
            .map(marks -> marks.subject() + "-" + marks.grade1())
            .collect(Collectors.toList());

    return LowerGradeReportDto.FirstTable.builder()
        .title("PART-I: SCHOLASTIC AREAS")
        .column1(constructColumn(configDetails.getFirst()))
        .column2(constructColumn(configDetails.get(1)))
        .column3(constructColumn(configDetails.get(2)))
        .column4(format("%s (%s)", "Total", total))
        .marks(firstTableMarks)
        .subjectGradeSlug(subjectGradeSlugs)
        .external(externalMarks)
        .totals(buildTotals(firstTableMarks, withMarks))
        .build();
  }

  public String constructColumn(ReportCardConfigDetail configDetail) {
    return Objects.isNull(configDetail)
        ? null
        : format(
            "%s (%s)", configDetail.getTermAssessment().getName(), configDetail.getWeightage());
  }

  public LowerGradeReportDto.SecondTable buildSecondTable(
      List<LowerGradeReportDto.SecondTableMarks> marks) {
    return LowerGradeReportDto.SecondTable.builder()
        .title("PART-II: Co-Scholastic Areas[on a 3-point(A to C)grading scale]")
        .marks(marks)
        .build();
  }

  public LowerGradeReportDto.ThirdTable buildThirdTable(
      List<LowerGradeReportDto.ThirdTableMarks> thirdTableMarks) {
    return LowerGradeReportDto.ThirdTable.builder().marks(thirdTableMarks).build();
  }

  private LowerGradeReportDto.TableMarks buildTableMarks(
      Student student, Boolean withMarks, String orgSlug) {

    var termAssessments =
        termAssessmentRepository.getAssessmentsByTermSlugAndGradeSlug(
            List.of("t1"), student.getSection().getGradeSlug());

    var termAssessmentIds = termAssessments.stream().map(TermAssessment::getId).toList();

    var data =
        reportCardConfigDataRepository.getStudentReportByStudentAndAssessments(
            student.getId(), termAssessmentIds);
    if (Objects.isNull(data) || data.isEmpty()) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "Report has not been generated");
    }

    var scholasticDataList =
        data.stream()
            .filter(
                x ->
                    SubjectsCategoryEnum.SCHOLASTIC.name().equals(x.getCategory())
                        && SubjectsTypeEnum.MANDATORY.name().equals(x.getType()))
            .toList();

    var optionalData =
        new ArrayList<>(
            data.stream()
                .filter(
                    x ->
                        SubjectsCategoryEnum.SCHOLASTIC.name().equals(x.getCategory())
                            && SubjectsTypeEnum.OPTIONAL.name().equals(x.getType()))
                .toList());

    var coScholasticData =
        data.stream()
            .filter(
                x ->
                    x.getCategory().equals(SubjectsCategoryEnum.CO_SCHOLASTIC.name())
                        && x.getType().equals(SubjectsTypeEnum.MANDATORY.name()))
            .toList();
    var coScholasticOptionalData =
        data.stream()
            .filter(
                x ->
                    x.getCategory().equals(SubjectsCategoryEnum.CO_SCHOLASTIC.name())
                        && x.getType().equals(SubjectsTypeEnum.OPTIONAL.name()))
            .toList();
    var gradeSlug = student.getSection().getGradeSlug();
    var sortedData =
        sortTable(
            buildTableMarks(scholasticDataList, withMarks, "", orgSlug),
            buildTableMarks(optionalData, withMarks, "", orgSlug),
            buildTableMarks(coScholasticData, false, "", orgSlug),
            buildTableMarks(coScholasticOptionalData, false, gradeSlug, orgSlug));
    return LowerGradeReportDto.TableMarks.builder()
        .firstTableMarks(sortedData.firstTableMarks())
        .externalMarks(sortedData.externalMarks())
        .secondTableMarks(sortedData.secondTableMarks())
        .thirdTableMarks(sortedData.thirdTableMarks())
        .build();
  }

  public List<Marks> buildTableMarks(
      List<LowerGradeReportCardData> reportCardData,
      Boolean withMarks,
      String gradeSlug,
      String orgSlug) {
    List<Marks> marksList = new ArrayList<>();

    var scholasticDataMap =
        reportCardData.stream()
            .collect(Collectors.groupingBy(LowerGradeReportCardData::getSubjectName));

    scholasticDataMap.forEach(
        (subject, scholasticData) -> {
          var pa1 = getMarks("pa1", scholasticData);
          var pa2 = getMarks("pa2", scholasticData);
          var hye = getMarks("hye", scholasticData);

          var term1Total = sumMarks(pa1, pa2, hye);
          var totalMarks =
              scholasticData.stream()
                  .filter(x -> x.getTotalMarks() != null)
                  .mapToDouble(LowerGradeReportCardData::getTotalMarks)
                  .sum();
          var isCoScholasticSubject =
              SubjectsCategoryEnum.CO_SCHOLASTIC
                  .name()
                  .equals(scholasticData.getFirst().getCategory());
          var isOptional =
              SubjectsTypeEnum.OPTIONAL.name().equals(scholasticData.getFirst().getType());
          var offlineTestDefinition =
              offlineTestScheduleService.validateOfflineTestDefinition(
                  scholasticData.getFirst().getOtdId());
          String grade = null;
          String description = null;
          String gradeLevel = null;
          if (isCoScholasticSubject && isOptional) {
            if (gradeSlug.equals("i")) {
              gradeLevel = "class1-grades";
            } else if (gradeSlug.equals("ii")) {
              gradeLevel = "class2-grades";
            }
            if (subject.equalsIgnoreCase("Numeracy Fluency Test")) {
              grade = calculateCoScholasticGrade(scholasticData.getFirst().getMarks(), "4point");
              description = getGradeContent("numeracy-fluency-test", gradeLevel, grade);
            } else {
              grade = calculateCoScholasticGrade(scholasticData.getFirst().getMarks(), "6point");
              description = getGradeContent("reading-fluency-test", gradeLevel, grade);
            }
          }
          marksList.add(
              Marks.builder()
                  .pa1(
                      Boolean.TRUE.equals(withMarks)
                          ? ((DPS_AEROCITY_SLUG.equals(orgSlug) && Boolean.TRUE.equals(isOptional))
                              ? calculateMarks("pa1", scholasticData)
                              : (Boolean.FALSE.equals(isOptional)
                                  ? String.valueOf(getMarks("pa1", scholasticData))
                                  : calculateMarks("pa1", scholasticData)))
                          : calculateMarks("pa1", scholasticData))
                  .pa2(
                      Boolean.TRUE.equals(withMarks)
                          ? ((DPS_AEROCITY_SLUG.equals(orgSlug) && Boolean.TRUE.equals(isOptional))
                              ? calculateMarks("pa2", scholasticData)
                              : (Boolean.FALSE.equals(isOptional)
                                  ? String.valueOf(getMarks("pa2", scholasticData))
                                  : calculateMarks("pa2", scholasticData)))
                          : calculateMarks("pa2", scholasticData))
                  .pa3(
                      Boolean.TRUE.equals(withMarks)
                          ? ((DPS_AEROCITY_SLUG.equals(orgSlug) && Boolean.TRUE.equals(isOptional))
                              ? calculateMarks("pa3", scholasticData)
                              : (Boolean.FALSE.equals(isOptional)
                                  ? String.valueOf(getMarks("pa1", scholasticData))
                                  : calculateMarks("pa3", scholasticData)))
                          : calculateMarks("pa3", scholasticData))
                  .hye(
                      Boolean.TRUE.equals(withMarks)
                          ? ((DPS_AEROCITY_SLUG.equals(orgSlug) && Boolean.TRUE.equals(isOptional))
                              ? calculateMarks("hye", scholasticData)
                              : (Boolean.FALSE.equals(isOptional)
                                  ? String.valueOf(getMarks("pa1", scholasticData))
                                  : calculateMarks("hye", scholasticData)))
                          : calculateMarks("hye", scholasticData))
                  .term1total(
                      Boolean.TRUE.equals(withMarks)
                          ? String.format("%.1f", term1Total)
                          : getTerm1Total(
                              isCoScholasticSubject,
                              isOptional,
                              term1Total,
                              offlineTestDefinition.getGradeScaleSlug(),
                              totalMarks))
                  .term1grade(
                      calculateGrade(
                          term1Total, totalMarks, offlineTestDefinition.getGradeScaleSlug()))
                  .overAllScored(term1Total)
                  .overAllExamMarks(totalMarks)
                  .grade2(
                      calculateGrade(
                          term1Total, totalMarks, offlineTestDefinition.getGradeScaleSlug()))
                  .subject(subject)
                  .seqNo(scholasticData.get(0).getSeqNo())
                  .otdId(scholasticData.getFirst().getOtdId())
                  .grade1(grade)
                  .overall(description)
                  .build());
        });
    return marksList;
  }

  public String getTerm1Total(
      boolean isCoScholasticSubject,
      boolean isOptional,
      Double term1Total,
      String gradeScaleSlug,
      double totalMarks) {
    if (isCoScholasticSubject) {
      return isOptional
          ? calculateCoScholasticGrade(term1Total, gradeScaleSlug)
          : calculateCoScholasticGrade(term1Total);
    }
    return calculateGrade(term1Total, totalMarks, gradeScaleSlug);
  }

  public Map<String, Object> loadGradesDescription() {
    try {
      var objectMapper = new ObjectMapper();
      return objectMapper.readValue(
          dpsGradesDescription.getInputStream(), new TypeReference<>() {});
    } catch (Exception e) {
      throw new RuntimeException("Failed to load grades description", e);
    }
  }

  public String getGradeContent(String subject, String gradeLevel, String grade) {
    Map<String, Object> gradesDescription = loadGradesDescription();

    Map<String, Object> subjectData = (Map<String, Object>) gradesDescription.get(subject);
    if (subjectData != null) {
      Map<String, String> gradeLevelData = (Map<String, String>) subjectData.get(gradeLevel);
      if (gradeLevelData != null) {
        String description = gradeLevelData.get(grade);
        if (description != null) {
          return description;
        }
      }
    }
    return null;
  }

  public String calculateMarks(String assessmentSlug, List<LowerGradeReportCardData> subjectData) {
    return subjectData.stream()
        .filter(data -> assessmentSlug.equalsIgnoreCase(data.getAssessmentSlug()))
        .findFirst()
        .map(
            data -> {
              if (data.getIsAttended() == null) {
                return null;
              } else if (Boolean.FALSE.equals(Boolean.valueOf(data.getIsAttended()))) {
                return Objects.isNull(data.getRemarks())
                    ? "AB"
                    : data.getRemarks().substring(0, 2).toUpperCase().toString();
              }

              var offlineTestDefinition =
                  offlineTestScheduleService.validateOfflineTestDefinition(data.getOtdId());

              if (Boolean.TRUE.equals(isCoScholasticAndOptional(data))) {
                return data.getMarks() != null
                    ? getTerm1Total(
                        true,
                        true,
                        data.getMarks(),
                        offlineTestDefinition.getGradeScaleSlug(),
                        data.getTotalMarks())
                    : null;
              }
              return data.getMarks() != null
                  ? calculateGrade(
                      data.getMarks(),
                      data.getTotalMarks(),
                      offlineTestDefinition.getGradeScaleSlug())
                  : null;
            })
        .orElse(null);
  }

  private boolean isCoScholasticAndOptional(LowerGradeReportCardData data) {
    return SubjectsCategoryEnum.CO_SCHOLASTIC.name().equals(data.getCategory())
        && SubjectsTypeEnum.OPTIONAL.name().equals(data.getType());
  }

  private String calculateGrade(Double marks, Double totalMarks, String gradeScaleSlug) {
    return marks == null || marks == 0 || Objects.isNull(totalMarks)
        ? null
        : pointScaleEvaluator.evaluate(
            gradeScaleSlug == null ? "4point" : gradeScaleSlug,
            BigDecimal.valueOf(
                Double.parseDouble(String.format("%.2f", (marks / totalMarks))) * 100));
  }

  private String calculateCoScholasticGrade(Double marks) {
    return marks == null || marks == 0
        ? null
        : pointScaleEvaluator.evaluate("3point", BigDecimal.valueOf(marks));
  }

  public String calculateCoScholasticGrade(Double marks, String gradeScaleSlug) {
    return marks == null || marks == 0
        ? null
        : pointScaleEvaluator.evaluate(gradeScaleSlug, BigDecimal.valueOf(marks));
  }

  public Double getMarks(String assessmentSlug, List<LowerGradeReportCardData> subjectData) {
    return subjectData.stream()
        .filter(data -> assessmentSlug.equalsIgnoreCase(data.getAssessmentSlug()))
        .map(LowerGradeReportCardData::getMarks)
        .mapToDouble(Double::doubleValue)
        .sum();
  }

  public Double sumMarks(Double... marks) {
    return Arrays.stream(marks).filter(Objects::nonNull).mapToDouble(Double::doubleValue).sum();
  }

  private LowerGradeReportDto.TableMarks sortTable(
      List<Marks> firstTableMarks,
      List<Marks> externalMarks,
      List<Marks> secondTableMarks,
      List<Marks> thirdTableMarks) {
    List<Marks> firstTable = new ArrayList<>();
    List<LowerGradeReportDto.SecondTableMarks> secondTable = new ArrayList<>();
    List<LowerGradeReportDto.ThirdTableMarks> thirdTable = new ArrayList<>();
    List<Marks> externalTable = new ArrayList<>();
    var sortedFirstTable =
        firstTableMarks.stream().sorted(Comparator.comparingLong(Marks::seqNo)).toList();
    s: // learn.academyteacher.com
    for (int i = 0; i < sortedFirstTable.size(); i++) {
      Marks mark = sortedFirstTable.get(i);
      firstTable.add(
          Marks.builder()
              .sno(i + 1L)
              .pa1(mark.pa1())
              .pa2(mark.pa2())
              .hye(mark.hye())
              .term1total(mark.term1total())
              .subject(mark.subject())
              .overall(mark.overall())
              .term1grade(mark.term1grade())
              .overAllExamMarks(mark.overAllExamMarks())
              .overAllScored(mark.overAllScored())
              .otdId(mark.otdId())
              .build());
    }

    var sortedThirdTable =
        thirdTableMarks.stream().sorted(Comparator.comparingLong(Marks::seqNo)).toList();
    for (int i = 0; i < sortedThirdTable.size(); i++) {
      Marks mark = sortedThirdTable.get(i);
      thirdTable.add(
          LowerGradeReportDto.ThirdTableMarks.builder()
              .sno(sortedFirstTable.size() + i + 1L)
              .subject(mark.subject())
              .term1grade(mark.grade1())
              .term1description(mark.overall())
              .build());
    }

    var sortedExternalTable =
        externalMarks.stream().sorted(Comparator.comparingLong(Marks::seqNo)).toList();
    for (int i = 0; i < sortedExternalTable.size(); i++) {
      Marks mark = sortedExternalTable.get(i);
      externalTable.add(
          Marks.builder()
              .sno(sortedFirstTable.size() + sortedThirdTable.size() + i + 1L)
              .pa1(mark.pa1())
              .pa2(mark.pa2())
              .hye(mark.hye())
              .grade2(mark.grade2())
              .term1total(mark.term1total())
              .subject(mark.subject())
              .overall(mark.overall())
              .build());
    }
    var sortedSecondTable =
        secondTableMarks.stream().sorted(Comparator.comparingLong(Marks::seqNo)).toList();
    for (int i = 0; i < sortedSecondTable.size(); i++) {
      Marks mark = sortedSecondTable.get(i);
      var term1total = mark.term1total() == null ? mark.hye() : mark.term1total();
      secondTable.add(
          LowerGradeReportDto.SecondTableMarks.builder()
              .term1Grade(term1total)
              .subjectName(mark.subject())
              .build());
    }
    return LowerGradeReportDto.TableMarks.builder()
        .firstTableMarks(firstTable)
        .externalMarks(externalTable)
        .secondTableMarks(secondTable)
        .thirdTableMarks(thirdTable)
        .build();
  }

  public LowerGradeReportDto.Totals buildTotals(List<Marks> firstTableMarks, Boolean withMarks) {
    var totalMarksScored =
        firstTableMarks.stream()
            .map(Marks::overAllScored)
            .filter(Objects::nonNull)
            .mapToDouble(Double::doubleValue)
            .sum();

    var totalMarks =
        firstTableMarks.stream()
            .map(Marks::overAllExamMarks)
            .filter(Objects::nonNull)
            .mapToDouble(Double::doubleValue)
            .sum();

    var offlineTestDef =
        offlineTestScheduleService.validateOfflineTestDefinition(
            firstTableMarks.getFirst().otdId());

    var totalPercentageScored = (totalMarksScored / totalMarks) * 100;

    String grade =
        totalMarksScored == 0
            ? "N/A"
            : pointScaleEvaluator.evaluate(
                offlineTestDef.getGradeScaleSlug() == null
                    ? "4point"
                    : offlineTestDef.getGradeScaleSlug(),
                BigDecimal.valueOf(totalPercentageScored));

    String overAllPercentage =
        Boolean.TRUE.equals(withMarks) ? String.format("%.2f", totalPercentageScored) : grade;

    return LowerGradeReportDto.Totals.builder()
        .overallPercentage(overAllPercentage)
        .overAllGrade(grade)
        .build();
  }

  public LowerGradeReportDto.Attendance buildAttendance(long studentId) {
    var termAssessment = termAssessmentRepository.findBySlug("hye");
    if (termAssessment.isEmpty()) {
      return LowerGradeReportDto.Attendance.builder().build();
    }
    var studentAttendance =
        offlineTestScheduleStudentAttendanceRepository.getOfflineTestStudentAttendance(
            studentId, termAssessment.get().getId());
    if (studentAttendance.isEmpty()) {
      return LowerGradeReportDto.Attendance.builder().build();
    }

    var testDefinition = studentAttendance.get().getOfflineTestDefinition();
    if (testDefinition == null) {
      throw new IllegalArgumentException("Test definition not found for studentId: " + studentId);
    }
    String totalAttendanceDays = testDefinition.getTotalAttendanceDays();
    Long daysPresent = studentAttendance.get().getPresentDays();
    if (totalAttendanceDays == null || daysPresent == null) {
      return LowerGradeReportDto.Attendance.builder().build();
    }

    Double attendancePercentage = null;
    double totalDays = Double.parseDouble(totalAttendanceDays);
    if (totalDays > 0.0) {
      attendancePercentage = (double) Math.round((daysPresent.doubleValue() / totalDays) * 100);
    }
    return LowerGradeReportDto.Attendance.builder()
        .workingDays(Long.valueOf(totalAttendanceDays))
        .daysPresent(daysPresent)
        .attendancePercentage(attendancePercentage)
        .remarks(studentAttendance.get().getRemarks())
        .build();
  }
}
