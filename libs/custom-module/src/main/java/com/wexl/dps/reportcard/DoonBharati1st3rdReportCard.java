package com.wexl.dps.reportcard;

import com.wexl.dps.dto.DoonBharatiLowerGradeDto;
import com.wexl.dps.managereportcard.repository.ReportCardConfigDataRepository;
import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.erp.attendance.dto.MedicalRecords;
import com.wexl.retail.erp.attendance.repository.SectionAttendanceDetailRepository;
import com.wexl.retail.erp.attendance.repository.SectionAttendanceRepository;
import com.wexl.retail.guardian.model.GuardianRole;
import com.wexl.retail.metrics.reportcards.ReportCardService;
import com.wexl.retail.model.Student;
import com.wexl.retail.model.User;
import com.wexl.retail.offlinetest.dto.ReportCardDto;
import com.wexl.retail.offlinetest.model.OfflineTestDefinition;
import com.wexl.retail.offlinetest.model.ReportCardTemplate;
import com.wexl.retail.offlinetest.repository.LowerGradeReportCardData;
import com.wexl.retail.offlinetest.repository.OfflineTestDefinitionRepository;
import com.wexl.retail.offlinetest.service.pointscale.PointScaleEvaluator;
import com.wexl.retail.organization.model.Organization;
import com.wexl.retail.services.StudentService;
import com.wexl.retail.student.attributes.model.StudentAttributeValueModel;
import com.wexl.retail.subjects.model.SubjectsCategoryEnum;
import com.wexl.retail.term.model.TermAssessment;
import com.wexl.retail.term.repository.TermAssessmentRepository;
import com.wexl.retail.term.repository.TermRepository;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@Slf4j
@RequiredArgsConstructor
public class DoonBharati1st3rdReportCard extends BaseReportCardDefinition {

  private final ReportCardService reportCardService;
  private final StudentService studentService;
  private final TermRepository termRepository;
  private final TermAssessmentRepository termAssessmentRepository;
  private final ReportCardConfigDataRepository reportCardConfigDataRepository;
  private final PointScaleEvaluator pointScaleEvaluator;
  private final OfflineTestDefinitionRepository offlineTestDefinitionRepository;
  private final SectionAttendanceRepository sectionAttendanceRepository;
  private final SectionAttendanceDetailRepository sectionAttendanceDetailRepository;
  private final LowerGradeFirstTermReportCard lowerGradeFirstTermReportCard;

  @Override
  public Optional<StudentAttributeValueModel> getStudentAttributeValue(
      Student student, String key) {
    return reportCardService.getStudentAttributeValue(student, "admission_no");
  }

  @Override
  public Map<String, Object> build(User user, Organization org, ReportCardDto.Request request) {
    var header = buildFirstToThirdHeader(user.getStudentInfo(), org);
    var body = buildBody(user, request, org);
    return Map.of("header", header, "pages", List.of(1), "body", body);
  }

  @Override
  public Map<String, Object> buildBody(User user, Organization org, ReportCardDto.Request request) {
    return null;
  }

  @Override
  public boolean supports(ReportCardTemplate reportCardTemplate) {
    final String config = reportCardTemplate.getConfig();
    return config.equals("doonbharthi-grade-1st-2nd-3rd-9th-report.xml");
  }

  private DoonBharatiLowerGradeDto.Header buildFirstToThirdHeader(
      Student student, Organization org) {

    var guardians = student.getGuardians();
    var father =
        guardians.stream().filter(x -> x.getRelationType().equals(GuardianRole.FATHER)).findAny();
    Optional<StudentAttributeValueModel> dateOfBirth =
        reportCardService.getStudentAttributeValue(student, "date_of_birth");
    Optional<StudentAttributeValueModel> address =
        reportCardService.getStudentAttributeValue(student, "residental_address");

    return DoonBharatiLowerGradeDto.Header.builder()
        .schoolName(org.getName())
        .sessionStart("25")
        .sessionEnd("26")
        .orgSlug(org.getSlug())
        .className(student.getSection().getGradeName())
        .gradeSlug(student.getSection().getGradeSlug())
        .sectionName(student.getSection().getName())
        .studentName(
            student.getUserInfo().getFirstName().concat(student.getUserInfo().getLastName()))
        .fatherName(
            father
                .map(guardian -> guardian.getFirstName() + " " + guardian.getLastName())
                .orElse(null))
        .dateOfBirth(dateOfBirth.map(StudentAttributeValueModel::getValue).orElse(""))
        .admissionNo(student.getRollNumber())
        .address(address.map(StudentAttributeValueModel::getValue).orElse(""))
        .rollNo(student.getRollNumber())
        .mobileNo(student.getUserInfo().getMobileNumber())
        .build();
  }

  private DoonBharatiLowerGradeDto.Body buildBody(
      User user, ReportCardDto.Request request, Organization org) {
    var student = user.getStudentInfo();
    var section = student.getSection();
    var attendance =
        sectionAttendanceRepository.getStudentsAttendanceReport(
            org.getSlug(),
            List.of(section.getGradeSlug()),
            List.of(section.getId()),
            List.of(1L),
            20250401,
            20250923);
    var studentsAttendance =
        attendance.stream()
            .filter(a -> student.getUserInfo().getAuthUserId().equals(a.getAuthId()))
            .findAny()
            .orElse(null);

    var termAssessments =
        termAssessmentRepository.getAssessmentsByTermSlugAndGradeSlug(
            Collections.singletonList("t1"), student.getSection().getGradeSlug());

    var termAssessmentIds = termAssessments.stream().map(TermAssessment::getId).toList();

    var data =
        reportCardConfigDataRepository.getStudentReportByStudentAndAssessments(
            student.getId(), termAssessmentIds);
    if (Objects.isNull(data) || data.isEmpty()) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "Report has not been generated");
    }
    var scholasticDataList =
        data.stream()
            .filter(x -> SubjectsCategoryEnum.SCHOLASTIC.name().equals(x.getCategory()))
            .toList();

    var coScholasticDataList =
        data.stream()
            .filter(x -> SubjectsCategoryEnum.CO_SCHOLASTIC.name().equals(x.getCategory()))
            .toList();

    var attendanceDetails = lowerGradeFirstTermReportCard.buildAttendance(student.getId());
    var studentData =
        sectionAttendanceDetailRepository.getMedicalRecords(student.getId(), org.getSlug());

    return DoonBharatiLowerGradeDto.Body.builder()
        .firstTableMarks(buildTableMarks(termAssessmentIds, student, scholasticDataList))
        .remarksTable(buildRemarksTable())
        .secondTable(buildSecondTable(coScholasticDataList))
        .isParticipatedInInternal(attendanceDetails.internalOlympiad())
        .isParticipated(attendanceDetails.olympiad())
        .olympiadSubjects(attendanceDetails.olympiadSubjects())
        .internalOlympiadSubjects(attendanceDetails.internalOlympiadSubjects())
        .totalWorkingDays(studentsAttendance != null ? studentsAttendance.getTotalWorkingDays() : 0)
        .totalPresentDays(studentsAttendance != null ? studentsAttendance.getPresentDays() : 0)
        .height(studentData.map(MedicalRecords::getHeight).orElse(null))
        .weight(studentData.map(MedicalRecords::getWeight).orElse(null))
        .term("t1")
        .overallRemark("")
        .promotedToNextClass(false)
        .build();
  }

  private DoonBharatiLowerGradeDto.FirstTableMarks buildTableMarks(
      List<Long> termIds, Student student, List<LowerGradeReportCardData> scholasticDataList) {

    return DoonBharatiLowerGradeDto.FirstTableMarks.builder()
        .marks(buildMarks(termIds, student, scholasticDataList))
        .build();
  }

  private List<DoonBharatiLowerGradeDto.Marks> buildMarks(
      List<Long> termAssessmentIds,
      Student student,
      List<LowerGradeReportCardData> reportCardData) {

    List<DoonBharatiLowerGradeDto.Marks> marksList = new ArrayList<>();

    var scholasticDataMap =
        reportCardData.stream()
            .sorted(Comparator.comparingLong(LowerGradeReportCardData::getSeqNo))
            .collect(
                Collectors.groupingBy(
                    LowerGradeReportCardData::getSubjectName,
                    LinkedHashMap::new,
                    Collectors.toList()));

    scholasticDataMap.forEach(
        (subject, scholasticData) -> {
          var lpl = getMarks("lpl", scholasticData);
          var pf = getMarks("portfolio", scholasticData);
          var cd = getMarks("cd", scholasticData);
          var ma = getMarks("ma", scholasticData);
          var mmt = getMarks("mmt", scholasticData);

          var marks = pf + cd + ma + mmt;
          var total = 100.0;

          var offlineTestDefinitionId = scholasticData.get(0).getOtdId();
          var offlineTestDefinition =
              offlineTestDefinitionRepository.findById(offlineTestDefinitionId);
          var gradeScaleSlug = offlineTestDefinition.map(OfflineTestDefinition::getGradeScaleSlug);

          marksList.add(
              DoonBharatiLowerGradeDto.Marks.builder()
                  .subjectName(subject)
                  .launchpadLearningGrade(String.valueOf(lpl))
                  .portFolio(pf)
                  .competitiveDevelopment(String.valueOf(cd))
                  .ma(ma)
                  .midMasteryTriumph(mmt)
                  .total(total)
                  .grade(calculateGrade(marks, total, String.valueOf(gradeScaleSlug)))
                  .build());
        });

    return marksList;
  }

  private DoonBharatiLowerGradeDto.SecondTable buildSecondTable(
      List<LowerGradeReportCardData> coScholasticDataList) {

    return DoonBharatiLowerGradeDto.SecondTable.builder()
        .secondTableMarks(buildSecondTableMarks(coScholasticDataList))
        .build();
  }

  private List<DoonBharatiLowerGradeDto.SecondTableMarks> buildSecondTableMarks(
      List<LowerGradeReportCardData> reportCardData) {

    List<DoonBharatiLowerGradeDto.SecondTableMarks> marksList = new ArrayList<>();

    var scholasticDataMap =
        reportCardData.stream()
            .sorted(Comparator.comparingLong(LowerGradeReportCardData::getSeqNo))
            .collect(
                Collectors.groupingBy(
                    LowerGradeReportCardData::getSubjectName,
                    LinkedHashMap::new,
                    Collectors.toList()));

    scholasticDataMap.forEach(
        (subject, scholasticData) -> {
          var mmt = getMarks("mmt", scholasticData);
          var ykv = getMarks("ykv", scholasticData);

          var offlineTestDefinitionId = scholasticData.get(0).getOtdId();
          var offlineTestDefinition =
              offlineTestDefinitionRepository.findById(offlineTestDefinitionId);

          marksList.add(
              DoonBharatiLowerGradeDto.SecondTableMarks.builder()
                  .subjectName(subject)
                  .midMasteryTriumph(calculateCoScholasticGrade(mmt, "5point"))
                  .yearlyKnowledgeVoyage(calculateCoScholasticGrade(ykv, "5point"))
                  .build());
        });

    return marksList;
  }

  private String calculateCoScholasticGrade(Double marks, String gradeScaleSlug) {
    return marks == null || marks == 0
        ? null
        : pointScaleEvaluator.evaluate(gradeScaleSlug, BigDecimal.valueOf(marks));
  }

  private List<DoonBharatiLowerGradeDto.RemarksTable> buildRemarksTable() {

    return Collections.singletonList(DoonBharatiLowerGradeDto.RemarksTable.builder().build());
  }

  private Double getMarks(String assessmentSlug, List<LowerGradeReportCardData> subjectData) {
    return subjectData.stream()
        .filter(data -> assessmentSlug.equalsIgnoreCase(data.getAssessmentSlug()))
        .map(LowerGradeReportCardData::getMarks)
        .mapToDouble(Double::doubleValue)
        .sum();
  }

  private String calculateGrade(Double marks, Double totalMarks, String gradeScaleSlug) {
    return marks == null || marks == 0 || Objects.isNull(totalMarks)
        ? null
        : pointScaleEvaluator.evaluate(
            gradeScaleSlug == null ? "5point" : gradeScaleSlug,
            BigDecimal.valueOf(
                Double.parseDouble(String.format("%.2f", (marks / totalMarks))) * 100));
  }
}
