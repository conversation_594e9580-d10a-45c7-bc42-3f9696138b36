package com.wexl.dps.reportcard;

import static java.lang.String.format;

import com.wexl.dps.dto.UpperGradeReportDto;
import com.wexl.dps.managereportcard.repository.ReportCardConfigDataRepository;
import com.wexl.dps.managereportcard.repository.ReportCardConfigRepository;
import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.metrics.reportcards.ReportCardService;
import com.wexl.retail.model.Student;
import com.wexl.retail.model.User;
import com.wexl.retail.offlinetest.dto.ReportCardDto;
import com.wexl.retail.offlinetest.model.ReportCardTemplate;
import com.wexl.retail.offlinetest.repository.LowerGradeReportCardData;
import com.wexl.retail.offlinetest.repository.OfflineTestScheduleStudentAttendanceRepository;
import com.wexl.retail.offlinetest.repository.UpperGradeReportCardData;
import com.wexl.retail.offlinetest.service.OfflineTestScheduleService;
import com.wexl.retail.offlinetest.service.pointscale.PointScaleEvaluator;
import com.wexl.retail.organization.model.Organization;
import com.wexl.retail.reportcards.model.ReportCardConfigDetail;
import com.wexl.retail.student.attributes.model.StudentAttributeValueModel;
import com.wexl.retail.student.attributes.service.StudentAttributeService;
import com.wexl.retail.subjects.model.SubjectsCategoryEnum;
import com.wexl.retail.subjects.model.SubjectsTypeEnum;
import com.wexl.retail.term.model.TermAssessment;
import com.wexl.retail.term.repository.TermAssessmentRepository;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class UpperGradeFirstTermReportCard extends BaseReportCardDefinition {

  private final ReportCardService reportCardService;
  private final TermAssessmentRepository termAssessmentRepository;
  private final ReportCardConfigDataRepository reportCardConfigDataRepository;
  private final OfflineTestScheduleStudentAttendanceRepository
      offlineTestScheduleStudentAttendanceRepository;
  private final PointScaleEvaluator pointScaleEvaluator;
  private final ReportCardConfigRepository reportCardConfigRepository;
  private final OfflineTestScheduleService offlineTestScheduleService;
  private final LowerGradeFirstTermReportCard lowerGradeFirstTermReportCard;
  private final StudentAttributeService studentAttributeService;
  private static final String FALSE = "false";

  @Override
  public Optional<StudentAttributeValueModel> getStudentAttributeValue(
      Student student, String key) {
    return reportCardService.getStudentAttributeValue(student, key);
  }

  @Override
  public Map<String, Object> build(User user, Organization org, ReportCardDto.Request request) {
    var header = buildHeader(user.getStudentInfo(), org);
    var body = buildBody(user, org.getSlug());
    return Map.of("header", header, "pages", List.of(1), "body", body);
  }

  @Override
  public Map<String, Object> buildBody(User user, Organization org, ReportCardDto.Request request) {
    return null;
  }

  @Override
  public boolean supports(ReportCardTemplate reportCardTemplate) {
    final String config = reportCardTemplate.getConfig();
    return config.equals("upper-grade-term1-report.xml");
  }

  public UpperGradeReportDto.Body buildBody(User user, String orgSlug) {
    var student = user.getStudentInfo();

    Optional<StudentAttributeValueModel> mother =
        reportCardService.getStudentAttributeValue(student, "mother_name");
    Optional<StudentAttributeValueModel> father =
        reportCardService.getStudentAttributeValue(student, "father_name");
    Optional<StudentAttributeValueModel> dateOfBirth =
        reportCardService.getStudentAttributeValue(student, "date_of_birth");
    var tableMarks = buildTableMarks(student);
    var testDefinition =
        offlineTestScheduleService.validateOfflineTestDefinition(
            tableMarks.firstTableMarks().getFirst().otdId());
    return UpperGradeReportDto.Body.builder()
        .name((user.getFirstName() + " " + user.getLastName()).toUpperCase())
        .dateOfBirth(studentAttributeService.getDateOfBirthFormat(dateOfBirth))
        .className(student.getSection().getName())
        .rollNumber(student.getClassRollNumber())
        .orgSlug(orgSlug)
        .gradeSlug(student.getSection().getGradeSlug())
        .mothersName(mother.map(StudentAttributeValueModel::getValue).orElse(null))
        .fathersName(father.map(StudentAttributeValueModel::getValue).orElse(null))
        .firstTable(
            buildFirstTable(tableMarks.firstTableMarks(), tableMarks.externalMarks(), student))
        .secondTable(buildSecondTable(tableMarks.secondTableMarks()))
        .attendance(buildAttendance(student.getId()))
        .gradeTable(UpperGradeReportDto.GradeTable.builder().title("Grade Scale").build())
        .gradingScale(
            testDefinition.getGradeScaleSlug() != null
                ? testDefinition.getGradeScaleSlug()
                : "8point")
        .build();
  }

  private UpperGradeReportDto.TableMarks buildTableMarks(Student student) {

    var termAssessments =
        termAssessmentRepository.getAssessmentsByTermSlugAndGradeSlug(
            Collections.singletonList("t1"), student.getSection().getGradeSlug());

    var termAssessmentIds = termAssessments.stream().map(TermAssessment::getId).toList();

    var data =
        reportCardConfigDataRepository.getStudentReportByStudentAndAssessments(
            student.getId(), termAssessmentIds);
    if (Objects.isNull(data) || data.isEmpty()) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "Report has not been generated");
    }

    var scholasticDataList =
        data.stream()
            .filter(
                x ->
                    SubjectsCategoryEnum.SCHOLASTIC.name().equals(x.getCategory())
                        && SubjectsTypeEnum.MANDATORY.name().equals(x.getType()))
            .toList();

    var optionalData =
        data.stream()
            .filter(
                x ->
                    SubjectsCategoryEnum.SCHOLASTIC.name().equals(x.getCategory())
                        && SubjectsTypeEnum.OPTIONAL.name().equals(x.getType()))
            .toList();

    var coScholasticData =
        data.stream()
            .filter(
                x ->
                    x.getCategory().equals(SubjectsCategoryEnum.CO_SCHOLASTIC.name())
                        && x.getType().equals(SubjectsTypeEnum.MANDATORY.name()))
            .toList();

    var sortedData =
        sortTable(
            buildTableMarks(scholasticDataList),
            buildTableMarks(optionalData),
            buildTableMarks(coScholasticData));
    return UpperGradeReportDto.TableMarks.builder()
        .firstTableMarks(sortedData.firstTableMarks())
        .externalMarks(sortedData.externalMarks())
        .secondTableMarks(sortedData.secondTableMarks())
        .build();
  }

  public List<UpperGradeReportDto.Marks> buildTableMarks(
      List<LowerGradeReportCardData> reportCardData) {
    List<UpperGradeReportDto.Marks> marksList = new ArrayList<>();

    var scholasticDataMap =
        reportCardData.stream()
            .sorted(Comparator.comparingLong(LowerGradeReportCardData::getSeqNo))
            .collect(
                Collectors.groupingBy(
                    LowerGradeReportCardData::getSubjectName,
                    LinkedHashMap::new,
                    Collectors.toList()));

    scholasticDataMap.forEach(
        (subject, scholasticData) -> {
          var pt = getMarks(List.of("pt(pt1+pt2)", "pa1+cra1"), scholasticData);
          var nb1 = getMarks(List.of("nb1"), scholasticData);
          var se1 = getMarks(List.of("se1"), scholasticData);
          var hye = getMarks(List.of("hye"), scholasticData);

          var term1Total = sumMarks(pt, nb1, se1, hye);
          var termMaxMarks = 100d;
          var offlineTestDefinition =
              offlineTestScheduleService.validateOfflineTestDefinition(
                  scholasticData.getFirst().getOtdId());
          String grade =
              calculateGrade(term1Total, termMaxMarks, offlineTestDefinition.getGradeScaleSlug());
          double term1TotalDouble = Double.parseDouble(String.format("%.1f", term1Total));

          marksList.add(
              UpperGradeReportDto.Marks.builder()
                  .pt(buildMarks(scholasticData, pt))
                  .nb1(buildMarks(scholasticData, se1))
                  .se1(buildMarks(scholasticData, nb1))
                  .hye(buildMarks(scholasticData, hye))
                  .grade1(grade)
                  .marksObtained1(term1TotalDouble)
                  .subject(subject)
                  .seqNo(scholasticData.get(0).getSeqNo())
                  .otdId(scholasticData.getFirst().getOtdId())
                  .build());
        });
    return marksList;
  }

  public String buildMarks(List<LowerGradeReportCardData> scholasticData, String value) {
    if (value == null) {
      return null;
    }
    return value.equals("0.0")
        ? (Boolean.TRUE.equals(Boolean.valueOf(scholasticData.getFirst().getIsAttended()))
            ? null
            : "AB")
        : value;
  }

  public String calculateGrade(Double marks, Double totalMarks, String gradeScaleSlug) {
    return marks == null || Objects.isNull(totalMarks)
        ? null
        : pointScaleEvaluator.evaluate(
            gradeScaleSlug == null ? "8point" : gradeScaleSlug,
            BigDecimal.valueOf(((marks / totalMarks) * 100)));
  }

  public String getMarks(List<String> assessmentSlug, List<LowerGradeReportCardData> subjectData) {
    Double average;
    var data =
        subjectData.stream().filter(d -> assessmentSlug.contains(d.getAssessmentSlug())).toList();
    if (data.isEmpty()) {
      return null;
    }

    var isAttended = data.stream().filter(d -> d.getIsAttended() == null).toList();
    if (!isAttended.isEmpty()) {
      return null;
    }

    var data1 =
        data.stream().filter(d -> Boolean.TRUE.equals(Boolean.valueOf(d.getIsAttended()))).toList();

    if (data1.isEmpty()) {
      return data.getFirst().getRemarks() == null
          ? "AB"
          : data.getFirst().getRemarks().substring(0, 2).toUpperCase();
    }

    average =
        data1.stream()
            .map(LowerGradeReportCardData::getMarks)
            .filter(Objects::nonNull)
            .mapToDouble(Double::doubleValue)
            .average()
            .orElse(0.0);

    return String.format("%.2f", average);
  }

  public Double sumMarks(String... marks) {
    return Arrays.stream(marks)
        .filter(mark -> mark != null && !mark.isEmpty() && mark.matches("\\d+(\\.\\d+)?"))
        .mapToDouble(Double::parseDouble)
        .sum();
  }

  private String calculateMarks(String assessmentSlug, List<UpperGradeReportCardData> subjectData) {
    return subjectData.stream()
        .filter(data -> assessmentSlug.equalsIgnoreCase(data.getAssessmentSlug()))
        .findFirst()
        .map(
            data -> {
              if (data.getIsAttended() == null) {
                return null;
              } else if (Boolean.FALSE.equals(data.getIsAttended())) {
                return "AB";
              }

              Double marks = data.getMarks() != null ? Double.valueOf(data.getMarks()) : null;
              Double total = data.getTotal() != null ? Double.valueOf(data.getTotal()) : null;
              var offlineTestDefinition =
                  offlineTestScheduleService.validateOfflineTestDefinition(data.getOtdId());
              return marks != null && total != null
                  ? calculateGrade(marks, total, offlineTestDefinition.getGradeScaleSlug())
                  : null;
            })
        .orElse(null);
  }

  public UpperGradeReportDto.FirstTable buildFirstTable(
      List<UpperGradeReportDto.Marks> firstTableMarks,
      List<UpperGradeReportDto.Marks> externalMarks,
      Student student) {
    var section = student.getSection();

    var reportCardConfigs =
        reportCardConfigRepository.findAllByOrgSlugAndBoardSlugAndGradeSlug(
            section.getOrganization(), section.getBoardSlug(), section.getGradeSlug());
    var configDetails =
        reportCardConfigs.getFirst().getReportCardConfigDetails().stream()
            .sorted(Comparator.comparing(ReportCardConfigDetail::getSeqNo))
            .toList();

    return UpperGradeReportDto.FirstTable.builder()
        .title("PART-I: SCHOLASTIC AREAS")
        .column1(constructColumn(configDetails.getFirst()))
        .column2(constructColumn(configDetails.get(1)))
        .column3(constructColumn(configDetails.get(2)))
        .column4(constructColumn(configDetails.get(3)))
        .marks(firstTableMarks)
        .external(externalMarks)
        .totals(buildTotals(firstTableMarks))
        .build();
  }

  private String constructColumn(ReportCardConfigDetail configDetail) {
    return Objects.isNull(configDetail)
        ? null
        : format(
            "%s (%s)", configDetail.getTermAssessment().getName(), configDetail.getWeightage());
  }

  public UpperGradeReportDto.Totals buildTotals(List<UpperGradeReportDto.Marks> firstTableMarks) {
    var totalMarksScored =
        firstTableMarks.stream()
            .map(UpperGradeReportDto.Marks::marksObtained1)
            .filter(Objects::nonNull)
            .mapToDouble(Double::doubleValue)
            .average();
    var totalMarks =
        Double.parseDouble(
            String.format(
                "%.1f",
                firstTableMarks.stream()
                    .map(UpperGradeReportDto.Marks::marksObtained1)
                    .filter(Objects::nonNull)
                    .mapToDouble(Double::doubleValue)
                    .sum()));
    var totalMarksScoredFormat = String.format("%.2f", totalMarksScored.orElse(0.0));

    String grade =
        totalMarksScored.isEmpty()
            ? "N/A"
            : pointScaleEvaluator.evaluate(
                "8point", BigDecimal.valueOf(Double.parseDouble(totalMarksScoredFormat)));

    String overallPercentageGrade = "N/A".equals(grade) ? "0.0" : grade;
    return UpperGradeReportDto.Totals.builder()
        .marksTotal(totalMarks)
        .overallPercentage(totalMarksScoredFormat)
        .grade(overallPercentageGrade)
        .build();
  }

  public UpperGradeReportDto.SecondTable buildSecondTable(
      List<UpperGradeReportDto.SecondTableMarks> marks) {
    return UpperGradeReportDto.SecondTable.builder()
        .title("PART-II: Co-Scholastic Areas[on a 3-point(A to C)grading scale]")
        .marks(marks)
        .build();
  }

  public UpperGradeReportDto.Attendance buildAttendance(Long studentId) {
    var termAssessment = termAssessmentRepository.findBySlug("hye");
    if (termAssessment.isEmpty()) {
      return UpperGradeReportDto.Attendance.builder().build();
    }
    var studentAttendance =
        offlineTestScheduleStudentAttendanceRepository.getOfflineTestStudentAttendance(
            studentId, termAssessment.get().getId());
    if (studentAttendance.isEmpty()) {
      return UpperGradeReportDto.Attendance.builder().build();
    }

    var testDefinition = studentAttendance.get().getOfflineTestDefinition();
    if (testDefinition == null) {
      throw new IllegalArgumentException("Test definition not found for studentId: " + studentId);
    }
    String totalAttendanceDays = testDefinition.getTotalAttendanceDays();
    Long daysPresent = studentAttendance.get().getPresentDays();
    if (totalAttendanceDays == null || daysPresent == null) {
      return UpperGradeReportDto.Attendance.builder().build();
    }

    Double attendancePercentage = null;
    double totalDays = Double.parseDouble(totalAttendanceDays);
    if (totalDays > 0.0) {
      attendancePercentage = (double) Math.round((daysPresent.doubleValue() / totalDays) * 100);
    }
    return UpperGradeReportDto.Attendance.builder()
        .workingDays(Long.valueOf(totalAttendanceDays))
        .daysPresent(daysPresent)
        .attendancePercentage(attendancePercentage)
        .remarks(studentAttendance.get().getRemarks())
        .build();
  }

  private UpperGradeReportDto.TableMarks sortTable(
      List<UpperGradeReportDto.Marks> firstTableMarks,
      List<UpperGradeReportDto.Marks> externalMarks,
      List<UpperGradeReportDto.Marks> secondTableMarks) {
    List<UpperGradeReportDto.Marks> firstTable = new ArrayList<>();
    List<UpperGradeReportDto.SecondTableMarks> secondTable = new ArrayList<>();
    List<UpperGradeReportDto.Marks> externalTable = new ArrayList<>();
    var sortedFirstTable =
        firstTableMarks.stream()
            .sorted(Comparator.comparingLong(UpperGradeReportDto.Marks::seqNo))
            .toList();
    s: // learn.academyteacher.com
    for (int i = 0; i < sortedFirstTable.size(); i++) {
      UpperGradeReportDto.Marks mark = sortedFirstTable.get(i);
      firstTable.add(
          UpperGradeReportDto.Marks.builder()
              .sno(i + 1L)
              .pt(mark.pt())
              .nb1(mark.nb1())
              .se1(mark.se1())
              .hye(mark.hye())
              .marksObtained1(mark.marksObtained1())
              .grade1(mark.grade1())
              .subject(mark.subject())
              .overAllGrade(mark.overAllGrade())
              .overAllScored(mark.overAllScored())
              .otdId(mark.otdId())
              .build());
    }

    var sortedExternalTable =
        externalMarks.stream()
            .sorted(Comparator.comparingLong(UpperGradeReportDto.Marks::seqNo))
            .toList();
    for (int i = 0; i < sortedExternalTable.size(); i++) {
      UpperGradeReportDto.Marks mark = sortedExternalTable.get(i);
      externalTable.add(
          UpperGradeReportDto.Marks.builder()
              .sno(sortedFirstTable.size() + i + 1L)
              .pt(mark.pt())
              .nb1(mark.nb1())
              .se1(mark.se1())
              .hye(mark.hye())
              .grade2(mark.grade1())
              .subject(mark.subject())
              .marksObtained2(mark.marksObtained1())
              .overAllGrade(mark.overAllGrade())
              .build());
    }
    var sortedSecondTable =
        secondTableMarks.stream()
            .sorted(Comparator.comparingLong(UpperGradeReportDto.Marks::seqNo))
            .toList();
    for (int i = 0; i < sortedSecondTable.size(); i++) {
      UpperGradeReportDto.Marks mark = sortedSecondTable.get(i);
      var hye = mark.hye();
      String grade = null;

      if (hye != null && !hye.equals("AB") && !hye.equals("PA") && !hye.equals("ML")) {
        grade = offlineTestScheduleService.getGrade(BigDecimal.valueOf(Double.parseDouble(hye)));
      }

      secondTable.add(
          UpperGradeReportDto.SecondTableMarks.builder()
              .term1Grade(Objects.isNull(grade) ? hye : grade)
              .subjectName(mark.subject())
              .build());
    }
    return UpperGradeReportDto.TableMarks.builder()
        .firstTableMarks(firstTable)
        .externalMarks(externalTable)
        .secondTableMarks(secondTable)
        .build();
  }
}
