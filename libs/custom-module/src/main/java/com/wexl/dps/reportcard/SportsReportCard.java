package com.wexl.dps.reportcard;

import com.wexl.dps.dto.SportsReportCardDto;
import com.wexl.dps.learningmilestones.model.*;
import com.wexl.dps.learningmilestones.repository.*;
import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.metrics.reportcards.ReportCardService;
import com.wexl.retail.model.Student;
import com.wexl.retail.model.User;
import com.wexl.retail.offlinetest.dto.ReportCardDto;
import com.wexl.retail.offlinetest.model.ReportCardTemplate;
import com.wexl.retail.organization.model.Organization;
import com.wexl.retail.repository.StudentRepository;
import com.wexl.retail.section.domain.Section;
import com.wexl.retail.section.service.SectionService;
import com.wexl.retail.student.attributes.model.StudentAttributeValueModel;
import com.wexl.retail.subjects.repository.SubjectsMetaDataRepository;
import java.util.*;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class SportsReportCard extends BaseReportCardDefinition {
  private final ReportCardService reportCardService;
  private final LmrCategoryRepository lmrCategoryRepository;
  private final LmrCategoryGradeRepository categoryGradeRepository;
  private final CategoryGradeAttributeRepository categoryGradeAttributeRepository;
  private final LmrCategoryAttributeDefinitionRepository lmrCategoryAttributeDefinitionRepository;
  private final SubjectsMetaDataRepository subjectsMetaDataRepository;
  private final LmrStudentDetailRepository lmrStudentDetailRepository;
  private final SectionService sectionService;
  private final StudentRepository studentRepository;

  @Override
  public Map<String, Object> build(User user, Organization org, ReportCardDto.Request request) {
    var header = buildHeader(user.getStudentInfo(), org);
    var body = buildBody(user, org.getSlug(), request);
    return Map.of("header", header, "pages", List.of(1), "body", body);
  }

  @Override
  public Map<String, Object> buildBody(User user, Organization org, ReportCardDto.Request request) {
    return null;
  }

  @Override
  public boolean supports(ReportCardTemplate reportCardTemplate) {
    final String config = reportCardTemplate.getConfig();
    return config.equals("sports-report-card.xml");
  }

  @Override
  public Optional<StudentAttributeValueModel> getStudentAttributeValue(
      Student student, String key) {
    return reportCardService.getStudentAttributeValue(student, "admission_no");
  }

  private SportsReportCardDto.Body buildBody(
      User user, String orgSlug, ReportCardDto.Request request) {
    Student student = studentRepository.findByUserId(user.getId());
    Section section = sectionService.findByUuid(student.getSection().getUuid().toString());
    var subjectMetadata =
        subjectsMetaDataRepository.findById(request.offlineTestDefinitionId()).orElseThrow();
    List<LmrCategoryGrade> lmrCategoryGrades =
        categoryGradeRepository.getAllByGradeSlugAndSubjectMetadataIdAndTerm(
            student.getSection().getGradeSlug(), subjectMetadata.getId(), request.termId());

    var lmrCategoryIds =
        lmrCategoryGrades.stream().map(LmrCategoryGrade::getLmrCategoryId).toList();
    var lmrCategories =
        lmrCategoryRepository.findByIdInAndType(
            new ArrayList<>(lmrCategoryIds), LmrCategoryType.EY_REPORT);
    List<SportsReportCardDto.Table> tables = new ArrayList<>();
    List<SportsReportCardDto.MainTable> mainTables = new ArrayList<>();
    for (LmrCategory lmrCategory :
        lmrCategories.stream().sorted(Comparator.comparing(LmrCategory::getSequence)).toList()) {
      var lmrGradeIds =
          lmrCategoryGrades.stream()
              .filter(lcg -> lcg.getLmrCategoryId().equals(lmrCategory.getId()))
              .map(LmrCategoryGrade::getId)
              .toList();
      var lmrCategoryAttributes =
          categoryGradeAttributeRepository.findByLmrCategoryGradeIdIn(lmrGradeIds);
      tables.add(buildTableRecords(lmrCategory, lmrCategoryAttributes));
      mainTables.add(
          buildMainTableDetails(user, lmrCategory, lmrCategoryAttributes, request.termId()));
    }
    SportsReportCardDto.Overall overall = getLearningLevelByListOfGrades(mainTables);

    return SportsReportCardDto.Body.builder()
        .orgSlug(orgSlug)
        .name(user.getFirstName() + " " + user.getLastName())
        .sectionName(section.getName())
        .studentId(student.getId())
        .rollNumber(student.getClassRollNumber())
        .overallLevel(overall.overallLevel())
        .overallGrade(overall.overallGrade())
        .gameTitle(subjectMetadata.getName())
        .table(tables)
        .mainTable(mainTables)
        .build();
  }

  public SportsReportCardDto.MainTable buildMainTableDetails(
      User user,
      LmrCategory lmrCategory,
      List<LmrCategoryGradeAttribute> lmrCategoryAttributes,
      Long termId) {
    List<String> areaOfLearning =
        lmrCategoryAttributes.stream().map(LmrCategoryGradeAttribute::getAttributeName).toList();
    List<Long> gradeAttributesIds =
        lmrCategoryAttributes.stream().map(LmrCategoryGradeAttribute::getId).toList();
    var lmrStudentDetailResponses =
        lmrStudentDetailRepository
            .findAllByOrgSlugAndStudentIdInAndTermIdAndLmrCategoryAttributeIdIn(
                user.getOrganization(),
                Collections.singletonList(user.getStudentInfo().getId()),
                termId,
                gradeAttributesIds);
    var marks =
        getMarks(
            lmrStudentDetailResponses.stream()
                .map(LmrStudentDetail::getSkillValue)
                .filter(Objects::nonNull)
                .toList());

    var sumMarks =
        lmrStudentDetailResponses.stream()
            .filter(lsd -> lsd.getSkillValue() != null)
            .mapToDouble(data -> Double.parseDouble(data.getSkillValue()))
            .sum();

    long skillValueSize = lmrStudentDetailResponses.stream().count();

    return SportsReportCardDto.MainTable.builder()
        .tableName(lmrCategory.getName())
        .AreaOfLearning(buildName(areaOfLearning))
        .LearningLevel(getLearningLevelByMarks(marks))
        .sumMarks(sumMarks)
        .skillValueSize(skillValueSize)
        .Grade(getGradeByMarks(marks))
        .build();
  }

  private double getMarks(List<String> marks) {
    if (marks == null || marks.isEmpty()) {
      return 0;
    }

    return marks.stream().filter(Objects::nonNull).mapToLong(Long::parseLong).average().orElse(0.0);
  }

  private String getGradeByMarks(double average) {
    if (average >= 4.0) {
      return "A+";
    } else if (average >= 3.0) {
      return "A";
    } else if (average >= 2.0) {
      return "B";
    } else if (average >= 1.0) {
      return "C";
    } else {
      return " ";
    }
  }

  private String getLearningLevelByMarks(double average) {
    if (average >= 4.0) {
      return "Excellent";
    } else if (average >= 3.0) {
      return "Good";
    } else if (average >= 2.0) {
      return "Satisfactory";
    } else if (average >= 1.0) {
      return "Needs Improvement";
    } else {
      return " ";
    }
  }

  public SportsReportCardDto.Overall getLearningLevelByListOfGrades(
      List<SportsReportCardDto.MainTable> mainTables) {
    double total = 0.0;
    Long size = 0L;
    for (var mainTable : mainTables) {
      total += mainTable.sumMarks();
      size += mainTable.skillValueSize();
    }
    double average = total / size;
    String level = getLearningLevelByMarks(average);
    String grade = getGradeByMarks(average);
    return SportsReportCardDto.Overall.builder().overallLevel(level).overallGrade(grade).build();
  }

  private String buildName(List<String> names) {
    if (names == null || names.isEmpty()) {
      return "";
    }
    if (names.size() == 1) {
      return names.get(0);
    }
    String allButLast = String.join(", ", names.subList(0, names.size() - 1));
    return allButLast + " & " + names.get(names.size() - 1);
  }

  private SportsReportCardDto.Table buildTableRecords(
      LmrCategory lmrCategory, List<LmrCategoryGradeAttribute> lmrCategoryAttributes) {
    List<SportsReportCardDto.AreaOfLearning> attributeList = new ArrayList<>();
    for (LmrCategoryGradeAttribute attribute : lmrCategoryAttributes) {
      var lmrAttributeDefinitions =
          lmrCategoryAttributeDefinitionRepository.findAllByLmrCategoryAttributeId(
              attribute.getId());
      var ratings =
          buildAttributeDefinitions(
              lmrAttributeDefinitions.stream()
                  .sorted(Comparator.comparing(LmrCategoryAttributeDefinition::getSeqNo))
                  .toList());
      attributeList.add(
          SportsReportCardDto.AreaOfLearning.builder()
              .areaOfLearning(attribute.getAttributeName())
              .ratings(ratings)
              .build());
    }
    return SportsReportCardDto.Table.builder()
        .tableTitle(lmrCategory.getName())
        .columns(getColumns())
        .attributes(attributeList)
        .build();
  }

  private SportsReportCardDto.Column buildAttributeDefinitions(
      List<LmrCategoryAttributeDefinition> lmrAttributeDefinitions) {
    if (lmrAttributeDefinitions.isEmpty()) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.DefinitionsNotFound");
    }
    return SportsReportCardDto.Column.builder()
        .column1(lmrAttributeDefinitions.get(0).getDefinition())
        .column2(lmrAttributeDefinitions.get(1).getDefinition())
        .column3(lmrAttributeDefinitions.get(2).getDefinition())
        .column4(lmrAttributeDefinitions.get(3).getDefinition())
        .build();
  }

  private SportsReportCardDto.Column getColumns() {
    return SportsReportCardDto.Column.builder()
        .column1("Excellent (A+)")
        .column2("Good (A)")
        .column3("Satisfactory (B)")
        .column4("Needs Improvement (C)")
        .build();
  }
}
