package com.wexl.dps.dto;

import com.wexl.gilico.dto.GilcoHolisticReportModeldto;
import java.util.List;
import lombok.Builder;

public class Gillco3rd5thHolisticReportDto {

  @Builder
  public record Model(Header header, Body body) {}

  @Builder
  public record Header(String imageUrl) {}

  @Builder
  public record Body(
      String name,
      String className,
      String sectionName,
      String rollNo,
      String admissionNumber,
      String house,
      String dateOfBirth,
      Integer age,
      String address,
      String fatherName,
      String motherName,
      GilcoHolisticReportModeldto.AllAboutMe allAboutMe,
      Attendance attendance,
      Interests interests,
      MedicalProfile medicalProfile,
      List<SelfAndPeerAssessments> selfAndPeerAssessments,
      Resource resources,
      List<ChildBetter> childBetter,
      Support support,
      List<ScholosticMandatory> scholosticMandatory,
      List<ScholosticOptional> scholosticOptional,
      List<ScholosticSkillDetails> scholosticSkillDetails,
      List<ScholosticSkillDetails> coScholosticSkillDetails,
      List<SummarySheetDetails> summarySheetDetails,
      List<ScholosticSkillDetails> classFacilitatorGroupedSkills,
      ParticipationAchievements participationAchievements,
      Facilitators facilitators,
      String classRemarks,
      String principleRemarks) {}

  @Builder
  public record ChildBetter(String statement, String term1Value, String term2Value) {}

  @Builder
  public record Resource(
      boolean books,
      boolean magazine,
      boolean toysAndGames,
      boolean tableOrMobile,
      boolean laptop,
      boolean internet) {}

  @Builder
  public record TableMarks(
      List<ScholosticMandatory> scholosticMandatory, List<ScholosticOptional> scholosticOptional) {}

  @Builder
  public record Facilitators(
      AreaOfStrength areaOfStrength,
      BarrierOfSuccess barrierOfSuccess,
      StudentProgress studentProgress) {}

  @Builder
  public record AreaOfStrength(
      boolean fb,
      boolean wi,
      boolean ec,
      boolean sft,
      boolean em,
      boolean op,
      boolean cs,
      boolean re,
      boolean cre,
      boolean con,
      boolean ao,
      String aoValue) {}

  @Builder
  public record BarrierOfSuccess(
      boolean loa,
      boolean lom,
      boolean lop,
      boolean pp,
      boolean ug,
      boolean di,
      boolean ibc,
      boolean sii,
      boolean none,
      boolean ao,
      String aoValue) {}

  @Builder
  public record StudentProgress(String help, String steps) {}

  @Builder
  public record ParticipationAchievements(
      String specificParticipation, String specificAchievements) {}

  @Builder
  public record SummarySheetDetails(String domainName, String term1Value, String term2Value) {}

  @Builder
  public record ScholosticSkillDetails(String subject, List<Skills> skills) {}

  @Builder
  public record Skills(String skillName, String term1Value, String term2Value) {}

  @Builder
  public record ScholosticOptional(
      String subject,
      String term1Pa,
      String term1Theory,
      String term1Practical,
      String term2Pa,
      String term2Theory,
      String term2Practical) {}

  @Builder
  public record ScholosticMandatory(
      String subject,
      String term1Pa,
      String term1NAP,
      String term1Sa,
      String term1Marks,
      String term1Total,
      String term2Pa,
      String term2NAP,
      String term2Sa,
      String term2Marks,
      String term2Total) {}

  @Builder
  public record Support(
      boolean englishReading,
      boolean englishCommunication,
      boolean hindiReading,
      boolean hindiCommunication,
      boolean punjabiReading,
      boolean punjabiCommunication,
      boolean reading,
      boolean numberAndMath,
      boolean selfConfidence,
      boolean workingWithChildren,
      boolean workingIndependently,
      String subjectAreas) {}

  @Builder
  public record SelfAndPeerAssessments(
      String skillName, String saTerm1, String saTerm2, String paTerm1, String paTerm2) {}

  @Builder
  public record MedicalProfile(
      String bloodGroup,
      String height,
      String weight,
      String dental,
      String rightEyeSight,
      String leftEyeSight) {}

  @Builder
  public record Interests(
      boolean reading,
      boolean dancing,
      boolean singing,
      boolean musicalInstrument,
      boolean sportsOrGames,
      boolean writing,
      boolean gardening,
      boolean yoga,
      boolean art,
      boolean craft,
      boolean cooking,
      boolean others,
      String specify) {}

  @Builder
  public record Attendance(Days workingDays, Days attendingDays, Days percentage) {}

  @Builder
  public record Days(
      String title,
      Long apr,
      Long may,
      Long jun,
      Long july,
      Long aug,
      Long sep,
      Long oct,
      Long nov,
      Long dec,
      Long jan,
      Long feb,
      Long mar) {}
}
