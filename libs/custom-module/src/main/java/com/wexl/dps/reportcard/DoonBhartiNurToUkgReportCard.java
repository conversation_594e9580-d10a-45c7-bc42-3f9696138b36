package com.wexl.dps.reportcard;

import com.wexl.dps.dto.DoonBhartiNurUkgReportDto;
import com.wexl.dps.learningmilestones.dto.LmrDto;
import com.wexl.dps.learningmilestones.dto.LmrStudentDto;
import com.wexl.dps.learningmilestones.model.LmrCategoryType;
import com.wexl.dps.learningmilestones.service.LmrStudentService;
import com.wexl.dps.managereportcard.repository.ReportCardConfigDataRepository;
import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.erp.attendance.dto.MedicalRecords;
import com.wexl.retail.erp.attendance.repository.SectionAttendanceDetailRepository;
import com.wexl.retail.erp.attendance.repository.SectionAttendanceRepository;
import com.wexl.retail.guardian.model.GuardianRole;
import com.wexl.retail.metrics.reportcards.ReportCardService;
import com.wexl.retail.model.Student;
import com.wexl.retail.model.User;
import com.wexl.retail.offlinetest.dto.ReportCardDto;
import com.wexl.retail.offlinetest.model.ReportCardTemplate;
import com.wexl.retail.offlinetest.repository.LowerGradeReportCardData;
import com.wexl.retail.organization.model.Organization;
import com.wexl.retail.section.domain.Section;
import com.wexl.retail.student.attributes.model.StudentAttributeValueModel;
import com.wexl.retail.subjects.model.SubjectsCategoryEnum;
import com.wexl.retail.subjects.model.SubjectsMetaData;
import com.wexl.retail.subjects.model.SubjectsTypeEnum;
import com.wexl.retail.subjects.repository.SubjectsMetaDataRepository;
import com.wexl.retail.telegram.service.UserService;
import com.wexl.retail.term.model.Term;
import com.wexl.retail.term.model.TermAssessment;
import com.wexl.retail.term.repository.TermAssessmentRepository;
import com.wexl.retail.term.repository.TermRepository;
import java.util.*;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@RequiredArgsConstructor
@Component
public class DoonBhartiNurToUkgReportCard extends BaseReportCardDefinition {

  private final ReportCardService reportCardService;
  private final UserService userService;
  private final TermAssessmentRepository termAssessmentRepository;
  private final ReportCardConfigDataRepository reportCardConfigDataRepository;
  private final Gillco3rd5thHolisticReportCard gillco3rd5thHolisticReportCard;
  private final LmrStudentService lmrStudentService;
  private final SubjectsMetaDataRepository subjectsMetaDataRepository;
  private final TermRepository termRepository;
  private final SectionAttendanceRepository sectionAttendanceRepository;
  private final LowerGradeFirstTermReportCard lowerGradeFirstTermReportCard;
  private final SectionAttendanceDetailRepository sectionAttendanceDetailRepository;

  private List<String> gradeSlugs = List.of("nur", "lkg", "ukg");

  @Override
  public Optional<StudentAttributeValueModel> getStudentAttributeValue(
      Student student, String key) {
    return reportCardService.getStudentAttributeValue(student, key);
  }

  @Override
  public boolean supports(ReportCardTemplate reportCardTemplate) {
    final String config = reportCardTemplate.getConfig();
    return config.equals("doon-bharti-nur-ukg-report.xml");
  }

  @Override
  public Map<String, Object> build(User user, Organization org, ReportCardDto.Request request) {
    var body = buildBody(user, org.getSlug());
    return Map.of("pages", List.of(1), "body", body);
  }

  @Override
  public Map<String, Object> buildBody(User user, Organization org, ReportCardDto.Request request) {
    return null;
  }

  private DoonBhartiNurUkgReportDto.Body buildBody(User user, String orgSlug) {

    Student student = user.getStudentInfo();
    Section section = student.getSection();
    var guardians = student.getGuardians();

    if (!gradeSlugs.contains(section.getGradeSlug())) {
      throw new ApiException(
          InternalErrorCodes.INVALID_REQUEST,
          "This report card is only for Nursery, LKG and UKG students");
    }

    var father =
        guardians.isEmpty()
            ? null
            : guardians.stream()
                .filter(x -> x.getRelationType().equals(GuardianRole.FATHER))
                .findFirst()
                .map(reportCardService::getGuardianName)
                .orElse(null);
    Optional<StudentAttributeValueModel> dateOfBirthOpt =
        reportCardService.getStudentAttributeValue(student, "date_of_birth");
    var dob = dateOfBirthOpt.map(StudentAttributeValueModel::getValue).orElse(null);
    Optional<StudentAttributeValueModel> addressOpt =
        reportCardService.getStudentAttributeValue(student, "residential_address");
    var address = addressOpt.map(StudentAttributeValueModel::getValue).orElse(null);

    DoonBhartiNurUkgReportDto.AcademicYear year = parseAcademicYear(student.getAcademicYearSlug());
    var data = buildTableMarks(student, orgSlug, user);

    var attendance =
        sectionAttendanceRepository.getStudentsAttendanceReport(
            orgSlug,
            List.of(section.getGradeSlug()),
            List.of(section.getId()),
            List.of(1L),
            20250401,
            20250923);
    var studentsAttendance =
        attendance.stream()
            .filter(a -> student.getUserInfo().getAuthUserId().equals(a.getAuthId()))
            .findAny()
            .orElse(null);

    var attendanceDetails = lowerGradeFirstTermReportCard.buildAttendance(student.getId());
    var studentData = sectionAttendanceDetailRepository.getMedicalRecords(student.getId(), orgSlug);

    return DoonBhartiNurUkgReportDto.Body.builder()
        .name(userService.getNameByUserInfo(student.getUserInfo()))
        .studentClass(section.getGradeName())
        .section(section.getName())
        .sessionStart(year.startYear())
        .sessionEnd(year.endYear())
        .term("t1")
        .dob(dob)
        .admissionNumber(student.getRollNumber())
        .rollNo(student.getClassRollNumber())
        .fatherName(father)
        .address(address)
        .phoneNumber(user.getMobileNumber())
        .learningVoyage(data.learningVoyage())
        .leftTable(data.leftTable())
        .rightTable(data.rightTable())
        .mathmaticalUnderstanding(data.mathmaticalUnderstanding())
        .totalAttendance(studentsAttendance != null ? studentsAttendance.getTotalWorkingDays() : 0)
        .studentAttendance(studentsAttendance != null ? studentsAttendance.getPresentDays() : 0)
        .schoolActivity(attendanceDetails.schoolActivityParticipation())
        .term1Height(studentData.map(MedicalRecords::getHeight).orElse(null))
        .term1Weight(studentData.map(MedicalRecords::getWeight).orElse(null))
        .build();
  }

  private DoonBhartiNurUkgReportDto.BuildTableMarks buildTableMarks(
      Student student, String orgSlug, User user) {
    var termAssessments =
        termAssessmentRepository.getAssessmentsByTermSlugAndGradeSlug(
            Arrays.asList("t1"), student.getSection().getGradeSlug());

    var termAssessmentIds = termAssessments.stream().map(TermAssessment::getId).toList();

    var data =
        reportCardConfigDataRepository.getStudentReportByStudentAndAssessments(
            student.getId(), termAssessmentIds);
    if (Objects.isNull(data) || data.isEmpty()) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "Report has not been generated");
    }

    var scholasticDataList =
        data.stream()
            .filter(
                x ->
                    SubjectsCategoryEnum.SCHOLASTIC.name().equals(x.getCategory())
                        && SubjectsTypeEnum.MANDATORY.name().equals(x.getType()))
            .toList();

    List<DoonBhartiNurUkgReportDto.LearningVoyage> learningVoyage =
        buildLearningVoyage(scholasticDataList);
    List<DoonBhartiNurUkgReportDto.RightTable> rightTable = buildRightTable(user, orgSlug);
    DoonBhartiNurUkgReportDto.LeftTable leftTable = buildLeftTable(user, orgSlug);
    List<DoonBhartiNurUkgReportDto.MathmaticalUnderstanding> mathematicalUnderstanding =
        buildMathematicalUnderstanding(user, orgSlug);

    return DoonBhartiNurUkgReportDto.BuildTableMarks.builder()
        .learningVoyage(learningVoyage)
        .leftTable(leftTable)
        .rightTable(rightTable)
        .mathmaticalUnderstanding(mathematicalUnderstanding)
        .build();
  }

  private List<DoonBhartiNurUkgReportDto.LearningVoyage> buildLearningVoyage(
      List<LowerGradeReportCardData> reportCardData) {
    List<DoonBhartiNurUkgReportDto.LearningVoyage> marksList = new ArrayList<>();

    var scholasticDataMap =
        reportCardData.stream()
            .sorted(Comparator.comparingLong(LowerGradeReportCardData::getSeqNo))
            .collect(
                Collectors.groupingBy(
                    LowerGradeReportCardData::getSubjectName,
                    LinkedHashMap::new,
                    Collectors.toList()));

    scholasticDataMap.forEach(
        (subject, scholasticData) -> {
          var ll = gillco3rd5thHolisticReportCard.getTermMarks("lpl", scholasticData);
          var mmt = gillco3rd5thHolisticReportCard.getTermMarks("mmt", scholasticData);
          var aa = gillco3rd5thHolisticReportCard.getTermMarks("aa", scholasticData);
          var ykv = gillco3rd5thHolisticReportCard.getTermMarks("ykv", scholasticData);

          marksList.add(
              DoonBhartiNurUkgReportDto.LearningVoyage.builder()
                  .subject(subject)
                  .ll(ll)
                  .mmt(mmt)
                  .aa(aa)
                  .ykv(ykv)
                  .build());
        });

    return marksList;
  }

  private List<DoonBhartiNurUkgReportDto.MathmaticalUnderstanding> buildMathematicalUnderstanding(
      User user, String orgSlug) {
    Section section = user.getStudentInfo().getSection();
    var student = user.getStudentInfo();

    List<SubjectsMetaData> subjectsMetaDataList =
        subjectsMetaDataRepository.findByOrgSlugAndWexlSubjectSlugInAndGradeSlugAndBoardSlug(
            orgSlug,
            List.of("mathematics-understanding"),
            section.getGradeSlug(),
            section.getBoardSlug());

    Optional<Term> termOpt = termRepository.findBySlug("t1");
    if (subjectsMetaDataList.isEmpty() || termOpt.isEmpty()) {
      return Collections.emptyList();
    }

    String sectionUuid = section.getUuid().toString();
    Long termId = termOpt.get().getId();

    List<DoonBhartiNurUkgReportDto.MathmaticalUnderstanding> result = new ArrayList<>();

    for (SubjectsMetaData sm : subjectsMetaDataList) {

      LmrDto.LmrResponse lmrResponse =
          lmrStudentService.getLearningMileStoneAttributes(
              section.getGradeSlug(), LmrCategoryType.EY_REPORT, sm.getId(), orgSlug, termId);

      for (LmrDto.LmrCategoryResponse cr : lmrResponse.categoryResponses()) {
        LmrStudentDto.LmrStudentResponse lmrStudentResponse =
            lmrStudentService.getLmrStudentDetail(
                orgSlug, sectionUuid, termId, cr.id(), sm.getId());

        if (lmrStudentResponse == null || lmrStudentResponse.studentResponses().isEmpty()) {
          continue;
        }

        Map<Long, String> skillMap =
            lmrStudentResponse.studentResponses().stream()
                .filter(sr -> sr.studentId().equals(student.getId()))
                .flatMap(sr -> sr.attributes().stream())
                .collect(
                    Collectors.toMap(
                        LmrStudentDto.AttributeResponse::attributeId,
                        LmrStudentDto.AttributeResponse::skillValue));

        for (LmrDto.LmrAttributes attr : cr.attributes()) {
          String skillValue = skillMap.get(attr.attributeId());

          DoonBhartiNurUkgReportDto.MathmaticalUnderstanding existing =
              result.stream()
                  .filter(d -> d.activity().equals(attr.attributeName()))
                  .findFirst()
                  .orElse(null);

          DoonBhartiNurUkgReportDto.MathmaticalUnderstanding.MathmaticalUnderstandingBuilder
              builder;
          if (existing != null) {
            result.remove(existing);
            builder =
                DoonBhartiNurUkgReportDto.MathmaticalUnderstanding.builder()
                    .activity(existing.activity())
                    .mmt(existing.mmt())
                    .ykv(existing.ykv());
          } else {
            builder =
                DoonBhartiNurUkgReportDto.MathmaticalUnderstanding.builder()
                    .activity(attr.attributeName());
          }
          builder.mmt(skillValue);
          result.add(builder.build());
        }
      }
    }

    return result;
  }

  private List<DoonBhartiNurUkgReportDto.RightTable> buildRightTable(User user, String orgSlug) {
    Section section = user.getStudentInfo().getSection();
    var student = user.getStudentInfo();

    List<SubjectsMetaData> subjectsMetaDataList =
        subjectsMetaDataRepository.findByOrgSlugAndWexlSubjectSlugInAndGradeSlugAndBoardSlug(
            orgSlug,
            List.of("co-curricular-activity", "work-habit", "socio-emotional-development"),
            section.getGradeSlug(),
            section.getBoardSlug());

    Optional<Term> termOpt = termRepository.findBySlug("t1");
    if (subjectsMetaDataList.isEmpty() || termOpt.isEmpty()) {
      return Collections.emptyList();
    }

    String sectionUuid = section.getUuid().toString();
    Long termId = termOpt.get().getId();

    List<DoonBhartiNurUkgReportDto.RightTable> result = new ArrayList<>();

    for (SubjectsMetaData sm : subjectsMetaDataList) {

      LmrDto.LmrResponse lmrResponse =
          lmrStudentService.getLearningMileStoneAttributes(
              section.getGradeSlug(), LmrCategoryType.EY_REPORT, sm.getId(), orgSlug, termId);

      List<DoonBhartiNurUkgReportDto.Details> detailsList = new ArrayList<>();

      for (LmrDto.LmrCategoryResponse cr : lmrResponse.categoryResponses()) {
        LmrStudentDto.LmrStudentResponse lmrStudentResponse =
            lmrStudentService.getLmrStudentDetail(
                orgSlug, sectionUuid, termId, cr.id(), sm.getId());

        if (lmrStudentResponse == null || lmrStudentResponse.studentResponses().isEmpty()) {
          continue;
        }

        Map<Long, String> skillMap =
            lmrStudentResponse.studentResponses().stream()
                .filter(sr -> sr.studentId().equals(student.getId()))
                .flatMap(sr -> sr.attributes().stream())
                .collect(
                    Collectors.toMap(
                        LmrStudentDto.AttributeResponse::attributeId,
                        LmrStudentDto.AttributeResponse::skillValue));

        for (LmrDto.LmrAttributes attr : cr.attributes()) {
          String skillValue = skillMap.get(attr.attributeId());

          DoonBhartiNurUkgReportDto.Details existing =
              detailsList.stream()
                  .filter(d -> d.milestone().equals(attr.attributeName()))
                  .findFirst()
                  .orElse(null);

          DoonBhartiNurUkgReportDto.Details.DetailsBuilder builder;
          if (existing != null) {
            detailsList.remove(existing);
            builder =
                DoonBhartiNurUkgReportDto.Details.builder()
                    .milestone(existing.milestone())
                    .mmt(existing.mmt())
                    .ykv(existing.ykv());
          } else {
            builder = DoonBhartiNurUkgReportDto.Details.builder().milestone(attr.attributeName());
          }
          builder.mmt(skillValue);
          detailsList.add(builder.build());
        }
      }

      result.add(
          DoonBhartiNurUkgReportDto.RightTable.builder()
              .name(sm.getName())
              .details(detailsList)
              .build());
    }

    return result;
  }

  private DoonBhartiNurUkgReportDto.LeftTable buildLeftTable(User user, String orgSlug) {
    Section section = user.getStudentInfo().getSection();

    List<SubjectsMetaData> subjectsMetaDataList =
        subjectsMetaDataRepository.findByOrgSlugAndWexlSubjectSlugInAndGradeSlugAndBoardSlug(
            orgSlug, List.of("hindi-", "english-"), section.getGradeSlug(), section.getBoardSlug());

    Optional<Term> termOpt = termRepository.findBySlug("t1");

    if (subjectsMetaDataList.isEmpty() || termOpt.isEmpty()) {
      return DoonBhartiNurUkgReportDto.LeftTable.builder()
          .listening(
              DoonBhartiNurUkgReportDto.Listening.builder().data(Collections.emptyList()).build())
          .reading(
              DoonBhartiNurUkgReportDto.Reading.builder().data(Collections.emptyList()).build())
          .writing(
              DoonBhartiNurUkgReportDto.Writing.builder().data(Collections.emptyList()).build())
          .build();
    }

    String sectionUuid = section.getUuid().toString();
    Long termId = termOpt.get().getId();
    var student = user.getStudentInfo();

    List<DoonBhartiNurUkgReportDto.Data> listeningData = new ArrayList<>();
    List<DoonBhartiNurUkgReportDto.Data> readingData = new ArrayList<>();
    List<DoonBhartiNurUkgReportDto.Data> writingData = new ArrayList<>();

    // Collect data subject-wise
    for (SubjectsMetaData sm : subjectsMetaDataList) {
      String subjectSlug = sm.getWexlSubjectSlug(); // e.g., "english-" or "hindi-"

      LmrDto.LmrResponse lmrResponse =
          lmrStudentService.getLearningMileStoneAttributes(
              section.getGradeSlug(), LmrCategoryType.EY_REPORT, sm.getId(), orgSlug, termId);

      for (LmrDto.LmrCategoryResponse cr : lmrResponse.categoryResponses()) {
        String name = cr.categoryName().toLowerCase();

        List<DoonBhartiNurUkgReportDto.Data> targetList;
        if (name.contains("listening")) {
          targetList = listeningData;
        } else if (name.contains("reading")) {
          targetList = readingData;
        } else if (name.contains("writing")) {
          targetList = writingData;
        } else {
          continue;
        }

        LmrStudentDto.LmrStudentResponse lmrStudentResponse =
            lmrStudentService.getLmrStudentDetail(
                orgSlug, sectionUuid, termId, cr.id(), sm.getId());
        if (lmrStudentResponse == null || lmrStudentResponse.studentResponses().isEmpty()) continue;

        Map<Long, String> skillMap =
            lmrStudentResponse.studentResponses().stream()
                .filter(sr -> sr.studentId().equals(student.getId()))
                .flatMap(sr -> sr.attributes().stream())
                .collect(
                    Collectors.toMap(
                        LmrStudentDto.AttributeResponse::attributeId,
                        LmrStudentDto.AttributeResponse::skillValue));

        for (LmrDto.LmrAttributes attr : cr.attributes()) {
          String skillValue = skillMap.get(attr.attributeId());

          DoonBhartiNurUkgReportDto.Data existing =
              targetList.stream()
                  .filter(d -> d.milestone().equals(attr.attributeName()))
                  .findFirst()
                  .orElse(null);

          // Create builder either from existing or new
          DoonBhartiNurUkgReportDto.Data.DataBuilder builder;
          if (existing != null) {
            targetList.remove(existing);
            builder =
                DoonBhartiNurUkgReportDto.Data.builder()
                    .milestone(existing.milestone())
                    .english_mmt(existing.english_mmt())
                    .hindi_mmt(existing.hindi_mmt())
                    .english_ykv(existing.english_ykv())
                    .hindi_ykv(existing.hindi_ykv());
          } else {
            builder = DoonBhartiNurUkgReportDto.Data.builder().milestone(attr.attributeName());
          }

          if (subjectSlug.contains("english")) {
            builder.english_mmt(skillValue);
          } else if (subjectSlug.contains("hindi")) {
            builder.hindi_mmt(skillValue);
          }
          targetList.add(builder.build());
        }
      }
    }

    return DoonBhartiNurUkgReportDto.LeftTable.builder()
        .listening(DoonBhartiNurUkgReportDto.Listening.builder().data(listeningData).build())
        .reading(DoonBhartiNurUkgReportDto.Reading.builder().data(readingData).build())
        .writing(DoonBhartiNurUkgReportDto.Writing.builder().data(writingData).build())
        .build();
  }

  public DoonBhartiNurUkgReportDto.AcademicYear parseAcademicYear(String slug) {
    if (slug != null && slug.contains("-")) {
      String[] parts = slug.split("-");
      if (parts.length == 2) {
        return new DoonBhartiNurUkgReportDto.AcademicYear(parts[0], parts[1]);
      }
    }
    return new DoonBhartiNurUkgReportDto.AcademicYear("", "");
  }
}
