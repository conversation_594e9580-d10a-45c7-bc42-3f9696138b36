package com.wexl.dps.reportcard;

import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.metrics.reportcards.ReportCardService;
import com.wexl.retail.model.Student;
import com.wexl.retail.model.User;
import com.wexl.retail.offlinetest.dto.ReportCardDto;
import com.wexl.retail.offlinetest.model.ReportCardTemplate;
import com.wexl.retail.organization.model.Organization;
import com.wexl.retail.student.attributes.model.StudentAttributeValueModel;
import com.wexl.retail.student.auth.StudentAuthService;
import com.wexl.retail.test.schedule.repository.ScheduleTestRepository;
import com.wexl.retail.test.schedule.repository.ScheduleTestStudentRepository;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@Slf4j
@RequiredArgsConstructor
public class SubjectiveCorrectionAnswerSheetReportCard extends BaseReportCardDefinition {

  private final ReportCardService reportCardService;
  private final StudentAuthService studentAuthService;
  private final ScheduleTestRepository scheduleTestRepository;
  private final ScheduleTestStudentRepository scheduleTestStudentRepository;

  @Override
  public Optional<StudentAttributeValueModel> getStudentAttributeValue(
      Student student, String key) {
    return reportCardService.getStudentAttributeValue(student, "admission_no");
  }

  @Override
  public Map<String, Object> build(User user, Organization org, ReportCardDto.Request request) {
    var header = buildHeader(user.getStudentInfo(), org);
    var body = buildBody(user, request.offlineTestDefinitionId());
    return Map.of("header", header, "pages", List.of(1), "body", body);
  }

  @Override
  public Map<String, Object> buildBody(User user, Organization org, ReportCardDto.Request request) {
    return null;
  }

  @Override
  public boolean supports(ReportCardTemplate reportCardTemplate) {
    final String config = reportCardTemplate.getConfig();
    List<String> templates =
        Arrays.asList(
            "10-mcq-report-card.xml", "physics-answer-sheet.xml", "physics-sc-answer-sheet.xml");
    return templates.stream().anyMatch(config::contains);
  }

  private ReportCardDto.Body buildBody(User user, Long testScheduleId) {
    var student = studentAuthService.validateStudentByUser(user);
    var scheduleTest =
        scheduleTestRepository
            .findById(testScheduleId)
            .orElseThrow(() -> new ApiException(InternalErrorCodes.INVALID_REQUEST, ""));
    var scheduleTestStudent =
        scheduleTestStudentRepository
            .findByScheduleTestAndStudent(scheduleTest, student.getUserInfo())
            .orElseThrow();
    return ReportCardDto.Body.builder()
        .name((user.getFirstName() + " " + user.getLastName()).toUpperCase())
        .rollNumber(student.getRollNumber())
        .build();
  }
}
