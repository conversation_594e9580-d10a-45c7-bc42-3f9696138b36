package com.wexl.dps.reportcard;

import com.wexl.dps.dto.QualitativeComparativeAnalysisDto;
import com.wexl.dps.managereportcard.repository.ReportCardConfigDetailRepository;
import com.wexl.dps.managereportcard.repository.ReportCardConfigRepository;
import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.content.model.Entity;
import com.wexl.retail.metrics.reportcards.ReportCardService;
import com.wexl.retail.model.Student;
import com.wexl.retail.model.User;
import com.wexl.retail.offlinetest.dto.ReportCardDto;
import com.wexl.retail.offlinetest.model.ReportCardTemplate;
import com.wexl.retail.offlinetest.repository.LowerGradeReportCardData;
import com.wexl.retail.offlinetest.repository.OfflineTestDefinitionRepository;
import com.wexl.retail.offlinetest.repository.OfflineTestScheduleStudentRepository;
import com.wexl.retail.offlinetest.service.OfflineTestScheduleService;
import com.wexl.retail.organization.model.Organization;
import com.wexl.retail.organization.repository.OrganizationRepository;
import com.wexl.retail.reportcards.model.ReportCardConfigDetail;
import com.wexl.retail.student.attributes.model.StudentAttributeValueModel;
import com.wexl.retail.subjects.model.SubjectsCategoryEnum;
import com.wexl.retail.telegram.service.UserService;
import com.wexl.retail.term.model.TermAssessmentCategory;
import com.wexl.retail.term.repository.TermAssessmentCategoryRepository;
import com.wexl.retail.util.StrapiService;
import java.util.*;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class QualitativeComparativeAnalysis extends BaseReportCardDefinition {
  private final ReportCardService reportCardService;
  private final UserService userService;
  private final OrganizationRepository organizationRepository;
  private final StrapiService strapiService;
  private final CannedReportCard cannedReportCard;
  private final OfflineTestScheduleService offlineTestScheduleService;
  private final OfflineTestScheduleStudentRepository offlineTestScheduleStudentRepository;
  private final OfflineTestDefinitionRepository offlineTestDefinitionRepository;
  private final ReportCardConfigRepository reportCardConfigRepository;
  private final ReportCardConfigDetailRepository reportCardConfigDetailRepository;
  private final TermAssessmentCategoryRepository termAssessmentCategoryRepository;

  @Override
  public Map<String, Object> build(User user, Organization org, ReportCardDto.Request request) {
    var header = buildHeader(user);
    var body = buildBody(user, org.getSlug(), request.offlineTestDefinitionId());
    return Map.of("header", header, "pages", List.of(2), "body", body);
  }

  @Override
  public Map<String, Object> buildBody(User user, Organization org, ReportCardDto.Request request) {
    return null;
  }

  @Override
  public boolean supports(ReportCardTemplate reportCardTemplate) {
    final String config = reportCardTemplate.getConfig();
    return config.equals("qualitative-comparative-analysis.xml");
  }

  @Override
  public Optional<StudentAttributeValueModel> getStudentAttributeValue(
      Student student, String key) {
    return reportCardService.getStudentAttributeValue(student, "admission_no");
  }

  public QualitativeComparativeAnalysisDto.Header buildHeader(User user) {
    var student = user.getStudentInfo();
    var organization = organizationRepository.findBySlug(user.getOrganization());
    Entity board = strapiService.getEduBoardById(student.getBoardId());
    var cannedHeader = cannedReportCard.buildHeader(student, organization);
    return QualitativeComparativeAnalysisDto.Header.builder()
        .admissionNumber(student.getRollNumber())
        .testType(cannedHeader.testType())
        .address(cannedHeader.address())
        .isoData(cannedHeader.isoData())
        .schoolName(cannedHeader.schoolName())
        .schoolLogo(cannedHeader.schoolLogo())
        .schoolWaterMark(cannedHeader.schoolWaterMark())
        .testName(cannedHeader.testName())
        .studentName((userService.getNameByUserInfo(user)).toUpperCase())
        .studentId(student.getId())
        .academicYear(cannedHeader.academicYear())
        .campus(organization.getName())
        .curriculum(board.getName())
        .build();
  }

  public QualitativeComparativeAnalysisDto.Body buildBody(
      User user, String orgSlug, Long offlineTestDefinitionId) {
    var student = user.getStudentInfo();
    var subjectMarks = buildTableMarks(student, orgSlug, offlineTestDefinitionId);
    var organization = organizationRepository.findBySlug(user.getOrganization());
    var cannedBody = cannedReportCard.buildBody(user, offlineTestDefinitionId, organization);
    var testDefinition =
        offlineTestScheduleService.validateOfflineTestDefinition(offlineTestDefinitionId);
    return QualitativeComparativeAnalysisDto.Body.builder()
        .name(cannedBody.name())
        .orgSlug(orgSlug)
        .gradeSlug(student.getSection().getGradeSlug())
        .rollNumber(cannedBody.rollNumber())
        .fathersName(cannedBody.fathersName())
        .gradingScale(cannedBody.gradingScale())
        .mothersName(cannedBody.mothersName())
        .testType(testDefinition.getTitle())
        .firstTable(cannedBody.firstTable())
        .secondTable(cannedBody.secondTable())
        .thirdTable(cannedBody.thirdTable())
        .dateOfBirth(cannedBody.dateOfBirth())
        .className(cannedBody.className())
        .attendance(cannedBody.attendance())
        .subjectPercentages(buildSubjectPercentages(subjectMarks))
        .testNames(getAssessmentCategoryNames(student, orgSlug, offlineTestDefinitionId))
        .colors(graphColors(subjectMarks))
        .build();
  }

  public QualitativeComparativeAnalysisDto.Colors graphColors(
      List<QualitativeComparativeAnalysisDto.Marks> subjectMarks) {
    String gray = null;
    String black = null;
    String pink = null;
    String green = null;

    for (QualitativeComparativeAnalysisDto.Marks subject : subjectMarks) {

      if (gray == null && subject.pt1Percentage() > 0.0) {
        gray = "gray";
      }

      if (black == null && subject.pt2Percentage() > 0.0) {
        black = "black";
      }

      if (pink == null && subject.pt3Percentage() > 0.0) {
        pink = "pink";
      }

      if (green == null && subject.pt4Percentage() > 0.0) {
        green = "green";
      }
    }
    return QualitativeComparativeAnalysisDto.Colors.builder()
        .gray(gray)
        .black(black)
        .pink(pink)
        .green(green)
        .build();
  }

  private List<QualitativeComparativeAnalysisDto.SubjectPercentage> buildSubjectPercentages(
      List<QualitativeComparativeAnalysisDto.Marks> subjectData) {
    List<QualitativeComparativeAnalysisDto.SubjectPercentage> response = new ArrayList<>();
    for (var data :
        subjectData.stream()
            .sorted(Comparator.comparing(QualitativeComparativeAnalysisDto.Marks::seqNo))
            .toList()) {
      response.add(
          QualitativeComparativeAnalysisDto.SubjectPercentage.builder()
              .seqNo(data.seqNo())
              .subjectName(data.subject())
              .pt1ActualPercentage(data.pt1Percentage() * 3)
              .pt2ActualPercentage(data.pt2Percentage() * 3)
              .pt3ActualPercentage(data.pt3Percentage() * 3)
              .pt4ActualPercentage(data.pt4Percentage() * 3)
              .build());
    }
    return response;
  }

  private List<QualitativeComparativeAnalysisDto.Marks> buildTableMarks(
      Student student, String orgSlug, Long offlineTestDefinitionId) {
    var section = student.getSection().getUuid().toString();

    var assessmentCategoryNames =
        getAssessmentCategoryNames(student, orgSlug, offlineTestDefinitionId);
    var data =
        offlineTestScheduleStudentRepository
            .getStudentMarksByStudentAndSectionAndAssessmentCategories(
                student.getId(),
                section,
                assessmentCategoryNames,
                SubjectsCategoryEnum.SCHOLASTIC.name());
    if (Objects.isNull(data) || data.isEmpty()) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "Report has not been generated");
    }
    return buildTableMarks(data);
  }

  private List<String> getAssessmentCategoryNames(
      Student student, String orgSlug, Long offlineTestDefinitionId) {
    var testDefinition = offlineTestDefinitionRepository.findById(offlineTestDefinitionId);
    var testName = testDefinition.get().getTitle();

    var reportCardConfig =
        reportCardConfigRepository.findByBoardSlugAndGradeSlugAndOrgSlugAndTitle(
            student.getSection().getBoardSlug(),
            student.getSection().getGradeSlug(),
            orgSlug,
            testName);
    if (reportCardConfig.isEmpty()) {
      return Collections.emptyList();
    }

    List<Long> reportCardConfigDetailIds =
        reportCardConfigDetailRepository
            .getReportCardConfigDetailsByConfigId(reportCardConfig.get().getId())
            .stream()
            .map(ReportCardConfigDetail::getId)
            .toList();

    List<Long> assessmentCategoryIds = new ArrayList<>();
    for (Long detailId : reportCardConfigDetailIds) {
      var reportCardConfigDetail =
          reportCardConfigDetailRepository
              .findById(detailId)
              .orElseThrow(
                  () ->
                      new ApiException(
                          InternalErrorCodes.INVALID_REQUEST,
                          "Report card config detail not found: " + detailId));

      var config = reportCardConfigDetail.getAssessmentEvaluationConfig();

      if (config != null && config.getAssessmentCategories() != null) {
        assessmentCategoryIds.addAll(config.getAssessmentCategories());
      }
    }

    return termAssessmentCategoryRepository.findAllById(assessmentCategoryIds).stream()
        .map(TermAssessmentCategory::getName)
        .toList();
  }

  public List<QualitativeComparativeAnalysisDto.Marks> buildTableMarks(
      List<LowerGradeReportCardData> reportCardData) {
    List<QualitativeComparativeAnalysisDto.Marks> marksList = new ArrayList<>();

    var scholasticDataMap =
        reportCardData.stream()
            .sorted(Comparator.comparingLong(LowerGradeReportCardData::getSeqNo))
            .collect(
                Collectors.groupingBy(
                    LowerGradeReportCardData::getSubjectName,
                    LinkedHashMap::new,
                    Collectors.toList()));
    scholasticDataMap.forEach(
        (subject, scholasticData) -> {
          var pa1 = getMarks("pa1", scholasticData);
          var pa2 = getMarks("pa2", scholasticData);
          var pa3 = getMarks("pa3", scholasticData);
          var pa4 = getMarks("pa4", scholasticData);

          marksList.add(
              QualitativeComparativeAnalysisDto.Marks.builder()
                  .seqNo(scholasticData.getFirst().getSeqNo())
                  .pt1Percentage(pa1)
                  .pt2Percentage(pa2)
                  .pt3Percentage(pa3)
                  .pt4Percentage(pa4)
                  .subject(subject)
                  .seqNo(scholasticData.get(0).getSeqNo())
                  .build());
        });
    return marksList;
  }

  private Double getMarks(String assessmentSlug, List<LowerGradeReportCardData> subjectData) {
    var marks =
        subjectData.stream()
            .filter(data -> assessmentSlug.equalsIgnoreCase(data.getAssessmentSlug()))
            .toList();
    if (!marks.isEmpty()) {
      var ptMarks =
          marks.getFirst().getMarks() == null
              ? 0.0
              : (marks.getFirst().getMarks() / marks.getFirst().getTotalMarks()) * 100;
      return ptMarks;
    }
    return 0.0;
  }
}
