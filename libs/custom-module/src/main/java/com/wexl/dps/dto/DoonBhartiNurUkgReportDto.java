package com.wexl.dps.dto;

import java.util.List;
import lombok.Builder;

public class DoonBhartiNurUkgReportDto {

  @Builder
  public record Model(Body body) {}

  @Builder
  public record Body(
      String name,
      String session,
      String studentClass,
      String section,
      String sessionStart,
      String sessionEnd,
      String term,
      String dob,
      String admissionNumber,
      String rollNo,
      String fatherName,
      String address,
      String phoneNumber,
      List<LearningVoyage> learningVoyage,
      LeftTable leftTable,
      List<RightTable> rightTable,
      List<MathmaticalUnderstanding> mathmaticalUnderstanding,
      String schoolActivity,
      String term1Height,
      String term1Weight,
      String term2Height,
      String term2Weight,
      Long totalAttendance,
      Long studentAttendance,
      String remarks,
      String yearEnd,
      String promotedClass) {}

  @Builder
  public record BuildTableMarks(
      List<LearningVoyage> learningVoyage,
      LeftTable leftTable,
      List<RightTable> rightTable,
      List<MathmaticalUnderstanding> mathmaticalUnderstanding) {}

  @Builder
  public record MathmaticalUnderstanding(String activity, String mmt, String ykv) {}

  @Builder
  public record RightTable(String name, List<Details> details) {}

  @Builder
  public record Details(String milestone, String mmt, String ykv) {}

  @Builder
  public record LeftTable(Listening listening, Reading reading, Writing writing) {}

  @Builder
  public record Writing(List<Data> data) {}

  @Builder
  public record Reading(List<Data> data) {}

  @Builder
  public record Listening(List<Data> data) {}

  @Builder
  public record Data(
      String milestone,
      String english_mmt,
      String hindi_mmt,
      String english_ykv,
      String hindi_ykv) {}

  @Builder
  public record LearningVoyage(String subject, String ll, String mmt, String aa, String ykv) {}

  @Builder
  public record AcademicYear(String startYear, String endYear) {}
}
