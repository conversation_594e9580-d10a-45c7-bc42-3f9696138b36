package com.wexl.dps.dto;

import lombok.Builder;

public class GuardianCardDto {
  @Builder
  public record Response(Header header) {}

  @Builder
  public record Header(
      String academicYear,
      String studentName,
      String rollNo,
      String sectionName,
      String admissionNo,
      String dob,
      String address,
      String studentPhoto,
      String fatherName,
      String fatherNumber,
      String fatherPhoto,
      String motherName,
      String motherNumber,
      String motherPhoto,
      String guardianName,
      String guardianPhoto,
      String guardianNumber) {}
}
