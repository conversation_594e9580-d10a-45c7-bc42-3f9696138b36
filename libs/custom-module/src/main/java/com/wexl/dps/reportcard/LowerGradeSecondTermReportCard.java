package com.wexl.dps.reportcard;

import static java.lang.String.format;

import com.wexl.dps.managereportcard.repository.ReportCardConfigDataRepository;
import com.wexl.dps.managereportcard.repository.ReportCardConfigRepository;
import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.metrics.reportcards.ReportCardService;
import com.wexl.retail.model.Student;
import com.wexl.retail.model.User;
import com.wexl.retail.offlinetest.dto.LowerGradeReportDto;
import com.wexl.retail.offlinetest.dto.ReportCardDto;
import com.wexl.retail.offlinetest.model.ReportCardTemplate;
import com.wexl.retail.offlinetest.repository.LowerGradeReportCardData;
import com.wexl.retail.offlinetest.repository.OfflineTestScheduleStudentAttendanceRepository;
import com.wexl.retail.offlinetest.service.OfflineTestScheduleService;
import com.wexl.retail.offlinetest.service.pointscale.PointScaleEvaluator;
import com.wexl.retail.organization.model.Organization;
import com.wexl.retail.reportcards.model.ReportCardConfigDetail;
import com.wexl.retail.student.attributes.model.StudentAttributeValueModel;
import com.wexl.retail.student.attributes.service.StudentAttributeService;
import com.wexl.retail.subjects.model.SubjectsCategoryEnum;
import com.wexl.retail.subjects.model.SubjectsTypeEnum;
import com.wexl.retail.term.model.TermAssessment;
import com.wexl.retail.term.repository.TermAssessmentRepository;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class LowerGradeSecondTermReportCard extends BaseReportCardDefinition {
  private final ReportCardService reportCardService;
  private final OfflineTestScheduleService offlineTestScheduleService;
  private final PointScaleEvaluator pointScaleEvaluator;
  private final OfflineTestScheduleStudentAttendanceRepository
      offlineTestScheduleStudentAttendanceRepository;
  private final ReportCardConfigDataRepository reportCardConfigDataRepository;
  private final TermAssessmentRepository termAssessmentRepository;
  private final ReportCardConfigRepository reportCardConfigRepository;
  private final StudentAttributeService studentAttributeService;
  private static final List<String> PAS_SLUGS = List.of("pa1", "pa2", "pa3", "pa4");

  @Override
  public Map<String, Object> build(User user, Organization org, ReportCardDto.Request request) {
    var header = buildHeader(user.getStudentInfo(), org);
    var body = buildBody(user, org.getSlug());
    return Map.of("header", header, "pages", List.of(1), "body", body);
  }

  @Override
  public Map<String, Object> buildBody(User user, Organization org, ReportCardDto.Request request) {
    return null;
  }

  @Override
  public boolean supports(ReportCardTemplate reportCardTemplate) {
    final String config = reportCardTemplate.getConfig();
    return config.equals("1st-5th-term2-report.xml");
  }

  @Override
  public Optional<StudentAttributeValueModel> getStudentAttributeValue(
      Student student, String key) {
    return reportCardService.getStudentAttributeValue(student, "admission_no");
  }

  public LowerGradeReportDto.Body buildBody(User user, String orgSlug) {
    var student = user.getStudentInfo();
    Optional<StudentAttributeValueModel> mother =
        reportCardService.getStudentAttributeValue(student, "mother_name");
    Optional<StudentAttributeValueModel> father =
        reportCardService.getStudentAttributeValue(student, "father_name");
    Optional<StudentAttributeValueModel> dateOfBirth =
        reportCardService.getStudentAttributeValue(student, "date_of_birth");
    var tableMarks = buildTableMarks(student);
    var testDefinition =
        offlineTestScheduleService.validateOfflineTestDefinition(
            tableMarks.firstTableMarks().getFirst().otdId());
    return LowerGradeReportDto.Body.builder()
        .name((user.getFirstName() + " " + user.getLastName()).toUpperCase())
        .dateOfBirth(studentAttributeService.getDateOfBirthFormat(dateOfBirth))
        .className(student.getSection().getName())
        .orgSlug(orgSlug)
        .gradeSlug(student.getSection().getGradeSlug())
        .rollNumber(student.getClassRollNumber())
        .mothersName(mother.map(StudentAttributeValueModel::getValue).orElse(null))
        .fathersName(father.map(StudentAttributeValueModel::getValue).orElse(null))
        .firstTable(
            buildFirstTable(tableMarks.firstTableMarks(), tableMarks.externalMarks(), student))
        .secondTable(buildSecondTable(tableMarks.secondTableMarks()))
        .attendance(buildAttendance(student.getId()))
        .gradeTable(LowerGradeReportDto.GradeTable.builder().title("Grade Scale").build())
        .gradingScale(
            testDefinition.getGradeScaleSlug() != null
                ? testDefinition.getGradeScaleSlug()
                : "4point")
        .build();
  }

  private LowerGradeReportDto.FirstTable buildFirstTable(
      List<LowerGradeReportDto.Marks> firstTableMarks,
      List<LowerGradeReportDto.Marks> externalMarks,
      Student student) {
    var section = student.getSection();

    var reportCardConfigs =
        reportCardConfigRepository.findAllByOrgSlugAndBoardSlugAndGradeSlug(
            section.getOrganization(), section.getBoardSlug(), section.getGradeSlug());
    var term1configDetails =
        reportCardConfigs.get(0).getReportCardConfigDetails().stream()
            .sorted(Comparator.comparing(ReportCardConfigDetail::getSeqNo))
            .toList();
    var term2configDetails =
        reportCardConfigs.get(1).getReportCardConfigDetails().stream()
            .sorted(Comparator.comparing(ReportCardConfigDetail::getSeqNo))
            .toList();

    var total =
        term1configDetails.stream()
            .filter(x -> x.getWeightage() != null)
            .mapToLong(ReportCardConfigDetail::getWeightage)
            .sum();
    List<String> subjectGradeSlugs =
        firstTableMarks.stream()
            .map(marks -> marks.subject() + "-" + marks.grade1())
            .collect(Collectors.toList());

    return LowerGradeReportDto.FirstTable.builder()
        .title("PART-I: SCHOLASTIC AREAS")
        .column1(constructColumn(term1configDetails.getFirst()))
        .column2(constructColumn(term1configDetails.get(1)))
        .column3(constructColumn(term1configDetails.get(2)))
        .column4(format("%s (%s)", "Total", total))
        .column5(constructColumn(term2configDetails.getFirst()))
        .column6(constructColumn(term2configDetails.get(1)))
        .column7(constructColumn(term2configDetails.get(2)))
        .column8(format("%s (%s)", "Total", total))
        .column9("PA'S TOTAL (20)")
        .column10(format("HYE+YE ToTAL (80)"))
        .column11(format("OverAll"))
        .marks(firstTableMarks)
        .subjectGradeSlug(subjectGradeSlugs)
        .external(externalMarks)
        .totals(buildTotals(firstTableMarks))
        .build();
  }

  private String constructColumn(ReportCardConfigDetail configDetail) {
    return Objects.isNull(configDetail)
        ? null
        : format(
            "%s (%s)", configDetail.getTermAssessment().getName(), configDetail.getWeightage());
  }

  private LowerGradeReportDto.SecondTable buildSecondTable(
      List<LowerGradeReportDto.SecondTableMarks> marks) {
    return LowerGradeReportDto.SecondTable.builder()
        .title("PART-II: Co-Scholastic Areas[on a 3-point(A to C)grading scale]")
        .marks(marks)
        .build();
  }

  private LowerGradeReportDto.TableMarks buildTableMarks(Student student) {

    var termAssessments =
        termAssessmentRepository.getAssessmentsByTermSlugAndGradeSlug(
            List.of("t1", "t2"), student.getSection().getGradeSlug());

    var termAssessmentIds = termAssessments.stream().map(TermAssessment::getId).toList();

    var data =
        reportCardConfigDataRepository.getStudentReportByStudentAndAssessments(
            student.getId(), termAssessmentIds);
    if (Objects.isNull(data) || data.isEmpty()) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "Report has not been generated");
    }

    var scholasticDataList =
        data.stream()
            .filter(
                x ->
                    SubjectsCategoryEnum.SCHOLASTIC.name().equals(x.getCategory())
                        && SubjectsTypeEnum.MANDATORY.name().equals(x.getType()))
            .toList();

    var optionalData =
        new ArrayList<>(
            data.stream()
                .filter(
                    x ->
                        SubjectsCategoryEnum.SCHOLASTIC.name().equals(x.getCategory())
                            && SubjectsTypeEnum.OPTIONAL.name().equals(x.getType()))
                .toList());

    var coScholasticData =
        data.stream()
            .filter(
                x ->
                    x.getCategory().equals(SubjectsCategoryEnum.CO_SCHOLASTIC.name())
                        && x.getType().equals(SubjectsTypeEnum.MANDATORY.name()))
            .toList();
    var coScholasticOptionalData =
        data.stream()
            .filter(
                x ->
                    x.getCategory().equals(SubjectsCategoryEnum.CO_SCHOLASTIC.name())
                        && x.getType().equals(SubjectsTypeEnum.OPTIONAL.name()))
            .toList();
    optionalData.addAll(0, coScholasticOptionalData);

    var sortedData =
        sortTable(
            buildTableMarks(scholasticDataList),
            buildTableMarks(optionalData),
            buildTableMarks(coScholasticData));
    return LowerGradeReportDto.TableMarks.builder()
        .firstTableMarks(sortedData.firstTableMarks())
        .externalMarks(sortedData.externalMarks())
        .secondTableMarks(sortedData.secondTableMarks())
        .build();
  }

  public List<LowerGradeReportDto.Marks> buildTableMarks(
      List<LowerGradeReportCardData> reportCardData) {
    List<LowerGradeReportDto.Marks> marksList = new ArrayList<>();

    var scholasticDataMap =
        reportCardData.stream()
            .sorted(Comparator.comparingLong(LowerGradeReportCardData::getSeqNo))
            .collect(
                Collectors.groupingBy(
                    LowerGradeReportCardData::getSubjectName,
                    LinkedHashMap::new,
                    Collectors.toList()));

    scholasticDataMap.forEach(
        (subject, scholasticData) -> {
          var pa1 = getMarks("pa1", scholasticData);
          var pa2 = getMarks("pa2", scholasticData);
          var hye = getMarks("hye", scholasticData);
          var pa3 = getMarks("pa3", scholasticData);
          var pa4 = getMarks("pa4", scholasticData);
          var ye = getMarks("ye", scholasticData);

          var termTotal = sumMarks(pa1, pa2, hye, pa3, pa4, ye);
          var term1total = sumMarks(pa1, pa2, hye);
          var term2total = sumMarks(pa3, pa4, ye);
          var pasTotal = sumMarks(pa1, pa2, pa3, pa4);
          var hyeYeTotal = sumMarks(hye, ye);
          var totalMarks =
              scholasticData.stream()
                  .filter(x -> x.getTotalMarks() != null)
                  .mapToDouble(LowerGradeReportCardData::getTotalMarks)
                  .sum();
          var isCoScholasticSubject =
              SubjectsCategoryEnum.CO_SCHOLASTIC
                  .name()
                  .equals(scholasticData.getFirst().getCategory());
          var isOptional =
              SubjectsTypeEnum.OPTIONAL.name().equals(scholasticData.getFirst().getType());
          var offlineTestDefinition =
              offlineTestScheduleService.validateOfflineTestDefinition(
                  scholasticData.getFirst().getOtdId());
          marksList.add(
              LowerGradeReportDto.Marks.builder()
                  .pa1(calculateMarks("pa1", scholasticData))
                  .pa2(calculateMarks("pa2", scholasticData))
                  .pa3(calculateMarks("pa3", scholasticData))
                  .pa4(calculateMarks("pa4", scholasticData))
                  .hye(calculateMarks("hye", scholasticData))
                  .ye(calculateMarks("ye", scholasticData))
                  .term1total(
                      getTermTotal(
                          isCoScholasticSubject,
                          isOptional,
                          term1total,
                          offlineTestDefinition.getGradeScaleSlug(),
                          50))
                  .term2total(
                      getTermTotal(
                          isCoScholasticSubject,
                          isOptional,
                          term2total,
                          offlineTestDefinition.getGradeScaleSlug(),
                          50))
                  .termTotal(
                      getTermTotal(
                          isCoScholasticSubject,
                          isOptional,
                          termTotal,
                          offlineTestDefinition.getGradeScaleSlug(),
                          totalMarks))
                  .pasTotal(
                      getTermTotal(
                          isCoScholasticSubject,
                          isOptional,
                          pasTotal,
                          offlineTestDefinition.getGradeScaleSlug(),
                          20))
                  .hyeYe(
                      getTermTotal(
                          isCoScholasticSubject,
                          isOptional,
                          hyeYeTotal,
                          offlineTestDefinition.getGradeScaleSlug(),
                          80))
                  .overAllScored(termTotal)
                  .overAllExamMarks(totalMarks)
                  .subject(subject)
                  .seqNo(scholasticData.get(0).getSeqNo())
                  .otdId(scholasticData.getFirst().getOtdId())
                  .build());
        });
    return marksList;
  }

  private String getTermTotal(
      boolean isCoScholasticSubject,
      boolean isOptional,
      Double marks,
      String gradeScaleSlug,
      double totalMarks) {
    if (isCoScholasticSubject) {
      return isOptional
          ? calculateCoScholasticGrade(marks, gradeScaleSlug)
          : calculateCoScholasticGrade(marks);
    }
    return calculateGrade(marks, totalMarks, gradeScaleSlug);
  }

  private String calculateMarks(String assessmentSlug, List<LowerGradeReportCardData> subjectData) {
    return subjectData.stream()
        .filter(data -> assessmentSlug.equalsIgnoreCase(data.getAssessmentSlug()))
        .findFirst()
        .map(
            data -> {
              if (data.getIsAttended() == null) {
                return null;
              } else if (Boolean.FALSE.equals(Boolean.valueOf(data.getIsAttended()))) {
                return Objects.isNull(data.getRemarks())
                    ? "AB"
                    : data.getRemarks().substring(0, 2).toUpperCase().toString();
              }

              var offlineTestDefinition =
                  offlineTestScheduleService.validateOfflineTestDefinition(data.getOtdId());

              if (Boolean.TRUE.equals(isCoScholasticAndOptional(data))) {
                return data.getMarks() != null
                    ? getTermTotal(
                        true,
                        true,
                        data.getMarks(),
                        offlineTestDefinition.getGradeScaleSlug(),
                        data.getTotalMarks())
                    : null;
              }
              return data.getMarks() != null
                  ? calculateGrade(
                      data.getMarks(),
                      data.getTotalMarks(),
                      offlineTestDefinition.getGradeScaleSlug())
                  : null;
            })
        .orElse(null);
  }

  private boolean isCoScholasticAndOptional(LowerGradeReportCardData data) {
    return SubjectsCategoryEnum.CO_SCHOLASTIC.name().equals(data.getCategory())
        && SubjectsTypeEnum.OPTIONAL.name().equals(data.getType());
  }

  private String calculateGrade(Double marks, Double totalMarks, String gradeScaleSlug) {
    return marks == null || marks == 0 || Objects.isNull(totalMarks)
        ? null
        : pointScaleEvaluator.evaluate(
            gradeScaleSlug == null ? "4point" : gradeScaleSlug,
            BigDecimal.valueOf(((marks / totalMarks) * 100)));
  }

  private String calculateCoScholasticGrade(Double marks) {
    return marks == null || marks == 0
        ? null
        : pointScaleEvaluator.evaluate("3point", BigDecimal.valueOf(marks));
  }

  private String calculateCoScholasticGrade(Double marks, String gradeScaleSlug) {
    return marks == null || marks == 0
        ? null
        : pointScaleEvaluator.evaluate(gradeScaleSlug, BigDecimal.valueOf(marks));
  }

  private Double getMarks(String assessmentSlug, List<LowerGradeReportCardData> subjectData) {
    return subjectData.stream()
        .filter(data -> assessmentSlug.equalsIgnoreCase(data.getAssessmentSlug()))
        .map(LowerGradeReportCardData::getMarks)
        .mapToDouble(Double::doubleValue)
        .sum();
  }

  private Double sumMarks(Double... marks) {
    return Arrays.stream(marks).filter(Objects::nonNull).mapToDouble(Double::doubleValue).sum();
  }

  private LowerGradeReportDto.TableMarks sortTable(
      List<LowerGradeReportDto.Marks> firstTableMarks,
      List<LowerGradeReportDto.Marks> externalMarks,
      List<LowerGradeReportDto.Marks> secondTableMarks) {
    List<LowerGradeReportDto.Marks> firstTable = new ArrayList<>();
    List<LowerGradeReportDto.SecondTableMarks> secondTable = new ArrayList<>();
    List<LowerGradeReportDto.Marks> externalTable = new ArrayList<>();
    var sortedFirstTable =
        firstTableMarks.stream()
            .sorted(Comparator.comparingLong(LowerGradeReportDto.Marks::seqNo))
            .toList();
    s: // learn.academyteacher.com
    for (int i = 0; i < sortedFirstTable.size(); i++) {
      LowerGradeReportDto.Marks mark = sortedFirstTable.get(i);
      firstTable.add(
          LowerGradeReportDto.Marks.builder()
              .sno(i + 1L)
              .pa1(mark.pa1())
              .pa2(mark.pa2())
              .hye(mark.hye())
              .pa3(mark.pa3())
              .pa4(mark.pa4())
              .ye(mark.ye())
              .pasTotal(mark.pasTotal())
              .hyeYe(mark.hyeYe())
              .term1total(mark.term1total())
              .term2total(mark.term2total())
              .termTotal(mark.termTotal())
              .subject(mark.subject())
              .overall(mark.overall())
              .overAllExamMarks(mark.overAllExamMarks())
              .overAllScored(mark.overAllScored())
              .otdId(mark.otdId())
              .build());
    }

    var sortedExternalTable =
        externalMarks.stream()
            .sorted(Comparator.comparingLong(LowerGradeReportDto.Marks::seqNo))
            .toList();
    for (int i = 0; i < sortedExternalTable.size(); i++) {
      LowerGradeReportDto.Marks mark = sortedExternalTable.get(i);
      externalTable.add(
          LowerGradeReportDto.Marks.builder()
              .sno(sortedFirstTable.size() + i + 1L)
              .pa1(mark.pa1())
              .pa2(mark.pa2())
              .hye(mark.hye())
              .pa3(mark.pa3())
              .pa4(mark.pa4())
              .ye(mark.ye())
              .pasTotal(mark.pasTotal())
              .hyeYe(mark.hyeYe())
              .term1total(mark.term1total())
              .term2total(mark.term2total())
              .termTotal(mark.termTotal())
              .subject(mark.subject())
              .overall(mark.overall())
              .build());
    }
    var sortedSecondTable =
        secondTableMarks.stream()
            .sorted(Comparator.comparingLong(LowerGradeReportDto.Marks::seqNo))
            .toList();
    for (int i = 0; i < sortedSecondTable.size(); i++) {
      LowerGradeReportDto.Marks mark = sortedSecondTable.get(i);
      var term1total = mark.term1total() == null ? mark.hye() : mark.term1total();
      var term2total = mark.term2total() == null ? mark.ye() : mark.term2total();
      secondTable.add(
          LowerGradeReportDto.SecondTableMarks.builder()
              .term1Grade(term1total)
              .term2Grade(term2total)
              .subjectName(mark.subject())
              .build());
    }
    return LowerGradeReportDto.TableMarks.builder()
        .firstTableMarks(firstTable)
        .externalMarks(externalTable)
        .secondTableMarks(secondTable)
        .build();
  }

  private LowerGradeReportDto.Totals buildTotals(List<LowerGradeReportDto.Marks> firstTableMarks) {
    var totalMarksScored =
        firstTableMarks.stream()
            .map(LowerGradeReportDto.Marks::overAllScored)
            .filter(Objects::nonNull)
            .mapToDouble(Double::doubleValue)
            .sum();

    var totalMarks =
        firstTableMarks.stream()
            .map(LowerGradeReportDto.Marks::overAllExamMarks)
            .filter(Objects::nonNull)
            .mapToDouble(Double::doubleValue)
            .sum();

    String grade =
        totalMarksScored == 0
            ? "N/A"
            : pointScaleEvaluator.evaluate(
                "4point", BigDecimal.valueOf((totalMarksScored / totalMarks) * 100));

    return LowerGradeReportDto.Totals.builder().overallPercentage(grade).build();
  }

  private LowerGradeReportDto.Attendance buildAttendance(long studentId) {
    var termAssessment = termAssessmentRepository.findBySlug("hye");
    if (termAssessment.isEmpty()) {
      return LowerGradeReportDto.Attendance.builder().build();
    }
    var studentAttendance =
        offlineTestScheduleStudentAttendanceRepository.getOfflineTestStudentAttendance(
            studentId, termAssessment.get().getId());
    if (studentAttendance.isEmpty()) {
      return LowerGradeReportDto.Attendance.builder().build();
    }

    var testDefinition = studentAttendance.get().getOfflineTestDefinition();
    if (testDefinition == null) {
      throw new IllegalArgumentException("Test definition not found for studentId: " + studentId);
    }
    String totalAttendanceDays = testDefinition.getTotalAttendanceDays();
    Long daysPresent = studentAttendance.get().getPresentDays();
    if (totalAttendanceDays == null || daysPresent == null) {
      return LowerGradeReportDto.Attendance.builder().build();
    }

    Double attendancePercentage = null;
    double totalDays = Double.parseDouble(totalAttendanceDays);
    if (totalDays > 0.0) {
      attendancePercentage = (double) Math.round((daysPresent.doubleValue() / totalDays) * 100);
    }
    return LowerGradeReportDto.Attendance.builder()
        .workingDays(Long.valueOf(totalAttendanceDays))
        .daysPresent(daysPresent)
        .attendancePercentage(attendancePercentage)
        .remarks(studentAttendance.get().getRemarks())
        .build();
  }
}
