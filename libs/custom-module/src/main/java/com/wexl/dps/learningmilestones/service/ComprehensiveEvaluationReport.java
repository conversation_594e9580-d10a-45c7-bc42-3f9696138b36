package com.wexl.dps.learningmilestones.service;

import static com.wexl.dps.learningmilestones.model.LmrCategoryType.COMPREHENSIVE;

import com.wexl.dps.DpsService;
import com.wexl.dps.dto.ComprehensiveReportDto;
import com.wexl.dps.learningmilestones.model.LmrCategoryGrade;
import com.wexl.dps.learningmilestones.model.LmrStudentDetailAchievement;
import com.wexl.dps.learningmilestones.repository.LmrCategoryGradeRepository;
import com.wexl.dps.learningmilestones.repository.LmrCategoryRepository;
import com.wexl.dps.learningmilestones.repository.LmrStudentDetailAchievementRepository;
import com.wexl.dps.reportcard.BaseReportCardDefinition;
import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.metrics.reportcards.ReportCardService;
import com.wexl.retail.model.Student;
import com.wexl.retail.model.User;
import com.wexl.retail.offlinetest.dto.ReportCardDto;
import com.wexl.retail.offlinetest.model.ReportCardTemplate;
import com.wexl.retail.organization.model.Organization;
import com.wexl.retail.services.StudentService;
import com.wexl.retail.student.attributes.model.StudentAttributeValueModel;
import com.wexl.retail.term.model.Term;
import com.wexl.retail.term.repository.TermRepository;
import java.util.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@Slf4j
@RequiredArgsConstructor
public class ComprehensiveEvaluationReport extends BaseReportCardDefinition {
  private final ReportCardService reportCardService;
  private final StudentService studentService;
  private final LmrStudentService lmrStudentService;
  private final LmrStudentDetailAchievementRepository lmrStudentDetailAchievementRepository;
  private final LmrCategoryGradeRepository lmrCategoryGradeRepository;
  private final LmrCategoryRepository lmrCategoryRepository;
  private final DpsService dpsService;
  private final TermRepository termRepository;

  @Override
  public Optional<StudentAttributeValueModel> getStudentAttributeValue(
      Student student, String key) {
    return reportCardService.getStudentAttributeValue(student, "admission_no");
  }

  @Override
  public Map<String, Object> build(User user, Organization org, ReportCardDto.Request request) {
    var header = buildHeader(user.getStudentInfo(), org);
    var body = buildBody(user, org, request.termId());
    return Map.of("header", header, "pages", List.of(1), "body", body);
  }

  @Override
  public Map<String, Object> buildBody(User user, Organization org, ReportCardDto.Request request) {
    return null;
  }

  @Override
  public boolean supports(ReportCardTemplate reportCardTemplate) {
    final String config = reportCardTemplate.getConfig();
    return config.equals("comprehensive-evaluation.xml");
  }

  public ComprehensiveReportDto.Body buildBody(User user, Organization organization, Long termId) {
    var student = studentService.findByUserInfo(user);
    Optional<LmrStudentDetailAchievement> studentAchievementDetail =
        lmrStudentDetailAchievementRepository.findByStudentId(student.getId());
    List<LmrCategoryGrade> lmrCategoryGrades =
        lmrCategoryGradeRepository.findAllByGradeSlug(student.getSection().getGradeSlug());
    var lmrCategoryIds =
        lmrCategoryGrades.stream().map(LmrCategoryGrade::getLmrCategoryId).toList();
    var lmrCategories =
        lmrCategoryRepository.findByIdInAndType(new ArrayList<>(lmrCategoryIds), COMPREHENSIVE);
    var lmrGradeData =
        lmrCategoryGradeRepository.findByLmrCategoryIdAndGradeSlugAndTermId(
            lmrCategories.getFirst().getId(), student.getSection().getGradeSlug(), termId);
    Long presentDays =
        studentAchievementDetail
            .map(LmrStudentDetailAchievement::getAttendancePresent)
            .orElse(null);
    Long totalDays = lmrGradeData.map(LmrCategoryGrade::getTotalWorkingDays).orElse(null);
    String attendanceValue = String.format("%s/%s", presentDays, totalDays);
    if (presentDays == null || totalDays == null) {
      attendanceValue = "NA";
    }
    Term term =
        termRepository
            .findById(termId)
            .orElseThrow(
                () -> new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.TermNotFound"));
    var data = dpsService.getDpsStudentAttendance(user.getExternalRef());
    return ComprehensiveReportDto.Body.builder()
        .name((user.getFirstName() + " " + user.getLastName()).toUpperCase())
        .rollNumber(student.getClassRollNumber())
        .className(student.getSection().getName())
        .gradeSlug(student.getSection().getGradeSlug())
        .termSlug(term.getSlug())
        .attendance(attendanceValue)
        .comments(
            studentAchievementDetail.map(LmrStudentDetailAchievement::getComments).orElse(null))
        .achievements(
            studentAchievementDetail.map(LmrStudentDetailAchievement::getAchievements).orElse(null))
        .firstTable(lmrStudentService.getEyLmrForComprehensive(student, termId))
        .attendanceTable(lmrStudentService.buildDpsStudentAttendance(data))
        .term1AttendanceTotal(lmrStudentService.buildTerm1AttendanceTotal(data))
        .term2AttendanceTotal(lmrStudentService.buildTerm2AttendanceTotal(data))
        .build();
  }
}
