package com.wexl.dps.reportcard;

import com.wexl.dps.dto.GillcoHolisticNurToUkgReportDto;
import com.wexl.gilico.service.GillcoHolisticReportCard;
import com.wexl.holisticreportcards.ProgressCardService;
import com.wexl.holisticreportcards.dto.ProgressCardDto;
import com.wexl.holisticreportcards.model.FacilitatorObservations;
import com.wexl.holisticreportcards.repository.FacilitatorObservationsRepository;
import com.wexl.holisticreportcards.repository.ProgressCardRepository;
import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.guardian.model.GuardianRole;
import com.wexl.retail.metrics.reportcards.ReportCardService;
import com.wexl.retail.model.Student;
import com.wexl.retail.model.User;
import com.wexl.retail.offlinetest.dto.ReportCardDto;
import com.wexl.retail.offlinetest.model.ReportCardTemplate;
import com.wexl.retail.organization.model.Organization;
import com.wexl.retail.student.attributes.model.StudentAttributeValueModel;
import com.wexl.retail.telegram.service.UserService;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class GillcoHolisticNurToUkgReportCard extends BaseReportCardDefinition {

  private final ReportCardService reportCardService;
  private final ProgressCardRepository progressCardRepository;
  private final ProgressCardService progressCardService;
  private final GillcoHolisticReportCard gillcoHolisticReportCard;
  private final UserService userService;
  private final Gillco3rd5thHolisticReportCard gillco3rd5thHolisticReportCard;
  private final FacilitatorObservationsRepository facilitatorObservationsRepository;

  private final List<String> gradeSlugs = List.of("nur", "lkg", "ukg");

  @Override
  public Optional<StudentAttributeValueModel> getStudentAttributeValue(
      Student student, String key) {
    return reportCardService.getStudentAttributeValue(student, key);
  }

  @Override
  public boolean supports(ReportCardTemplate reportCardTemplate) {
    final String config = reportCardTemplate.getConfig();
    return config.equals("gillco-holistic-nur-ukg-report.xml");
  }

  @Override
  public Map<String, Object> build(User user, Organization org, ReportCardDto.Request request) {
    var header = gillco3rd5thHolisticReportCard.buildHolisticHeader(user);
    var body = BuildGillcoBody(user, org.getSlug());
    return Map.of("header", header, "pages", List.of(1), "body", body);
  }

  public GillcoHolisticNurToUkgReportDto.Body BuildGillcoBody(User user, String orgSlug) {

    var student = user.getStudentInfo();
    var guardians = student.getGuardians();
    var section = student.getSection();

    if (!gradeSlugs.contains(section.getGradeSlug())) {
      throw new ApiException(
          InternalErrorCodes.INVALID_REQUEST,
          "The grade '"
              + section.getGradeSlug()
              + "' is not supported for the Gillco Holistic Nursery to UKG Report.");
    }

    var mother =
        guardians.isEmpty()
            ? null
            : guardians.stream()
                .filter(x -> x.getRelationType().equals(GuardianRole.MOTHER))
                .findFirst()
                .map(reportCardService::getGuardianName)
                .orElse(null);
    var father =
        guardians.isEmpty()
            ? null
            : guardians.stream()
                .filter(x -> x.getRelationType().equals(GuardianRole.FATHER))
                .findFirst()
                .map(reportCardService::getGuardianName)
                .orElse(null);
    Optional<StudentAttributeValueModel> dateOfBirthOpt =
        reportCardService.getStudentAttributeValue(student, "date_of_birth");
    var dateOfBirth = dateOfBirthOpt.map(StudentAttributeValueModel::getValue).orElse(null);
    Optional<StudentAttributeValueModel> house =
        reportCardService.getStudentAttributeValue(student, "house");
    Optional<StudentAttributeValueModel> address =
        reportCardService.getStudentAttributeValue(student, "residential_address");
    Integer age = gillcoHolisticReportCard.getAge(dateOfBirth);
    var studentData = progressCardRepository.findByStudentId(student.getId());
    List<ProgressCardDto.Competencies> competencies =
        progressCardService.getCompetencyDetails(section.getGradeSlug(), orgSlug, student);
    Optional<FacilitatorObservations> facilitatorObservations =
        facilitatorObservationsRepository.findByStudentId(student.getId());

    return GillcoHolisticNurToUkgReportDto.Body.builder()
        .name(userService.getNameByUserInfo(student.getUserInfo()))
        .className(student.getSection().getGradeName())
        .sectionName(student.getSection().getName())
        .rollNo(student.getClassRollNumber())
        .admissionNumber(student.getRollNumber())
        .house(house.map(StudentAttributeValueModel::getValue).orElse(null))
        .dateOfBirth(dateOfBirth)
        .age(age)
        .address(address.map(StudentAttributeValueModel::getValue).orElse(null))
        .fatherName(father)
        .motherName(mother)
        .allAboutMe(gillcoHolisticReportCard.buildAllAboutMe(studentData))
        .term("t1")
        .term1(buildTerm1(competencies, facilitatorObservations))
        .term2(buildTerm2(competencies, facilitatorObservations))
        .build();
  }

  private GillcoHolisticNurToUkgReportDto.Term1 buildTerm2(
      List<ProgressCardDto.Competencies> competencies,
      Optional<FacilitatorObservations> facilitator) {
    String term = "term2";
    return GillcoHolisticNurToUkgReportDto.Term1.builder()
        .communicativeDevelopment(BuildCommunicativeDevelopment(competencies, term))
        .cognitiveDevelopment(buildDevelopment(competencies, term, "cognitive-development"))
        .physicalDevelopment(buildCategoryAndCriteria(competencies, term, "physical-development"))
        .socioEmotionalDevelopment(
            buildDevelopment(competencies, term, "socio-emotional-development"))
        .learningSkills(buildDevelopment(competencies, term, "learning-skills"))
        .thematicConcepts(facilitator != null ? facilitator.get().getTerm2thematicConcept() : null)
        .remarks(facilitator != null ? facilitator.get().getClassFacilitatorRemarks() : null)
        .principalRemarks(facilitator != null ? facilitator.get().getPrincipalRemarks() : null)
        .build();
  }

  private GillcoHolisticNurToUkgReportDto.Term1 buildTerm1(
      List<ProgressCardDto.Competencies> competencies,
      Optional<FacilitatorObservations> facilitator) {
    String term = "term1";
    return GillcoHolisticNurToUkgReportDto.Term1.builder()
        .communicativeDevelopment(BuildCommunicativeDevelopment(competencies, term))
        .cognitiveDevelopment(buildDevelopment(competencies, term, "cognitive-development"))
        .physicalDevelopment(buildCategoryAndCriteria(competencies, term, "physical-development"))
        .socioEmotionalDevelopment(
            buildDevelopment(competencies, term, "socio-emotional-development"))
        .learningSkills(buildDevelopment(competencies, term, "learning-skills"))
        .thematicConcepts(facilitator != null ? facilitator.get().getTerm1thematicConcept() : null)
        .remarks(facilitator != null ? facilitator.get().getClassFacilitatorRemarks() : null)
        .principalRemarks(null)
        .build();
  }

  private GillcoHolisticNurToUkgReportDto.CommunicativeDevelopment BuildCommunicativeDevelopment(
      List<ProgressCardDto.Competencies> competencies, String term) {
    return GillcoHolisticNurToUkgReportDto.CommunicativeDevelopment.builder()
        .english(buildCategoryAndCriteria(competencies, term, "english"))
        .hindi(buildCategoryAndCriteria(competencies, term, "hindi"))
        .build();
  }

  public List<GillcoHolisticNurToUkgReportDto.CategoryAndCriteria> buildCategoryAndCriteria(
      List<ProgressCardDto.Competencies> competencies, String subjectSlug, String term) {

    return competencies.stream()
        .filter(c -> c.subjectSlug().equals(subjectSlug))
        .flatMap(
            c ->
                c.skills().stream()
                    .map(
                        skill -> {
                          List<GillcoHolisticNurToUkgReportDto.Criteria> criteriaList =
                              skill.competencyDetails().stream()
                                  .map(
                                      cd -> {
                                        String value = null;
                                        if ("term1".equals(term)) {
                                          value = cd.term1() != null ? cd.term1().getValue() : null;
                                        } else if ("term2".equals(term)) {
                                          value = cd.term2() != null ? cd.term2().getValue() : null;
                                        }

                                        return GillcoHolisticNurToUkgReportDto.Criteria.builder()
                                            .value(value)
                                            .skill(cd.name())
                                            .build();
                                      })
                                  .toList();

                          return GillcoHolisticNurToUkgReportDto.CategoryAndCriteria.builder()
                              .category(skill.skillName())
                              .criteria(criteriaList)
                              .build();
                        }))
        .toList();
  }

  private List<GillcoHolisticNurToUkgReportDto.Development> buildDevelopment(
      List<ProgressCardDto.Competencies> competencies, String term, String subjectSlug) {

    return competencies.stream()
        .filter(c -> subjectSlug.equals(c.subjectSlug()))
        .flatMap(c -> c.skills().stream())
        .flatMap(
            skill ->
                skill.competencyDetails().stream()
                    .map(
                        cd -> {
                          String value = null;
                          if (term.equals("term1")) {
                            value = cd.term1() != null ? cd.term1().getValue() : null;
                          } else if (term.equals("term2")) {
                            value = cd.term2() != null ? cd.term2().getValue() : null;
                          }

                          return GillcoHolisticNurToUkgReportDto.Development.builder()
                              .subject(cd.name())
                              .skillValue(value)
                              .build();
                        }))
        .toList();
  }

  @Override
  public Map<String, Object> buildBody(User user, Organization org, ReportCardDto.Request request) {
    return Map.of();
  }
}
