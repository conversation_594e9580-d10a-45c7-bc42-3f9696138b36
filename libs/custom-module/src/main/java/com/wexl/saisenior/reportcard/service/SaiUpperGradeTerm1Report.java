package com.wexl.saisenior.reportcard.service;

import com.wexl.dps.managereportcard.repository.ReportCardConfigRepository;
import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.guardian.model.GuardianRole;
import com.wexl.retail.metrics.reportcards.ReportCardService;
import com.wexl.retail.model.Student;
import com.wexl.retail.model.User;
import com.wexl.retail.offlinetest.model.ReportCardTemplate;
import com.wexl.retail.offlinetest.repository.LowerGradeReportCardData;
import com.wexl.retail.offlinetest.repository.OfflineTestScheduleStudentAttendanceRepository;
import com.wexl.retail.offlinetest.service.pointscale.PointScaleEvaluator;
import com.wexl.retail.organization.model.Organization;
import com.wexl.retail.reportcards.model.ReportCardConfigDetail;
import com.wexl.retail.student.attributes.model.StudentAttributeValueModel;
import com.wexl.retail.subjects.model.SubjectsCategoryEnum;
import com.wexl.retail.subjects.model.SubjectsTypeEnum;
import com.wexl.retail.term.model.TermAssessment;
import com.wexl.retail.term.repository.TermAssessmentRepository;
import com.wexl.retail.term.repository.TermRepository;
import com.wexl.saisenior.SaiSeniorBaseReportCardDefinition;
import com.wexl.saisenior.reportcard.dto.ReportCardDto;
import com.wexl.saisenior.reportcard.dto.SaiSeniorUpperGradeDto;
import com.wexl.saisenior.reportcard.repository.SaiSeniorRepository;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Stream;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class SaiUpperGradeTerm1Report extends SaiSeniorBaseReportCardDefinition {
  private final ReportCardService reportCardService;
  private final SaiSeniorRepository saiSeniorRepository;
  private final TermAssessmentRepository termAssessmentRepository;
  private final ReportCardConfigRepository reportCardConfigRepository;
  private final PointScaleEvaluator pointScaleEvaluator;
  private final OfflineTestScheduleStudentAttendanceRepository
      offlineTestScheduleStudentAttendanceRepository;
  private final TermRepository termRepository;

  @Override
  public Map<String, Object> build(
      User user, Organization org, com.wexl.retail.offlinetest.dto.ReportCardDto.Request request) {
    var header = buildHeader(user.getStudentInfo(), org);
    var body = buildBody(user);
    return Map.of("header", header, "pages", List.of(1), "body", body);
  }

  @Override
  public Map<String, Object> buildBody(
      User user, Organization org, com.wexl.retail.offlinetest.dto.ReportCardDto.Request request) {
    return null;
  }

  @Override
  public boolean supports(ReportCardTemplate reportCardTemplate) {
    final String config = reportCardTemplate.getConfig();
    return config.equals("sai-senior-6th-8th-term1-report.xml");
  }

  public SaiSeniorUpperGradeDto.Body buildBody(User user) {
    var student = user.getStudentInfo();
    var guardians = student.getGuardians();

    var father =
        guardians.stream().filter(x -> x.getRelationType().equals(GuardianRole.FATHER)).findAny();
    var mother =
        guardians.stream().filter(x -> x.getRelationType().equals(GuardianRole.MOTHER)).findAny();
    Optional<StudentAttributeValueModel> dateOfBirth =
        reportCardService.getStudentAttributeValue(student, "date_of_birth");
    DateTimeFormatter outputFormatter = DateTimeFormatter.ofPattern("dd-MM-yyyy");
    String formattedDateOfBirth =
        dateOfBirth
            .map(StudentAttributeValueModel::getValue)
            .map(dateString -> LocalDate.parse(dateString))
            .map(date -> date.format(outputFormatter))
            .orElse(null);
    Optional<StudentAttributeValueModel> admissionNo =
        reportCardService.getStudentAttributeValue(student, "admission_no");
    var termAssessments =
        termAssessmentRepository.getAssessmentsByTermSlugAndGradeSlug(
            Collections.singletonList("t1"), student.getSection().getGradeSlug());
    var studentAttendance = getAttendance(student.getId());
    var termAssessmentIds = termAssessments.stream().map(TermAssessment::getId).toList();
    var marks =
        saiSeniorRepository.getStudentReportByStudentAndAssessments(
            student.getId(), termAssessmentIds);
    if (Objects.isNull(marks) || marks.isEmpty()) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "Report has not been generated");
    }
    var firstTable = buildFirstTable(marks, student);
    return SaiSeniorUpperGradeDto.Body.builder()
        .name((user.getFirstName() + " " + user.getLastName()).toUpperCase())
        .dateOfBirth(formattedDateOfBirth)
        .className(student.getSection().getName())
        .rollNumber(student.getRollNumber())
        .admissionNo(admissionNo.map(StudentAttributeValueModel::getValue).orElse(null))
        .fathersName(
            father
                .map(guardian -> guardian.getFirstName() + " " + guardian.getLastName())
                .orElse(null))
        .mothersName(
            mother
                .map(guardian -> guardian.getFirstName() + " " + guardian.getLastName())
                .orElse(null))
        .firstTable(firstTable)
        .secondTable(buildSecondTable(marks))
        .thirdTable(buildThirdTable(firstTable, studentAttendance))
        .generalRemark(studentAttendance.remarks())
        .build();
  }

  private SaiSeniorUpperGradeDto.ThirdTable buildThirdTable(
      SaiSeniorUpperGradeDto.FirstTable firstTable, ReportCardDto.Attendance studentAttendance) {
    return SaiSeniorUpperGradeDto.ThirdTable.builder()
        .result(calculateOverAllGrade(firstTable))
        .academic(buildPercentage(firstTable))
        .overall(calculateOverAllPercentage(firstTable))
        .attendance(studentAttendance.attendancePercentage())
        .build();
  }

  private double buildPercentage(SaiSeniorUpperGradeDto.FirstTable firstTable) {
    double totalMarks =
        Stream.of(
                firstTable.subject1(),
                firstTable.subject2(),
                firstTable.subject3(),
                firstTable.subject4(),
                firstTable.subject5())
            .flatMap(List::stream)
            .map(SaiSeniorUpperGradeDto.Marks::grade)
            .mapToDouble(Double::doubleValue)
            .sum();

    return totalMarks > 0 ? formatMarks(totalMarks / 5) : 0.0;
  }

  private double calculateOverAllPercentage(SaiSeniorUpperGradeDto.FirstTable firstTable) {
    double totalMarks =
        Stream.of(
                firstTable.subject1(),
                firstTable.subject2(),
                firstTable.subject3(),
                firstTable.subject4(),
                firstTable.subject5())
            .flatMap(List::stream)
            .map(SaiSeniorUpperGradeDto.Marks::percentage)
            .mapToDouble(Double::doubleValue)
            .sum();

    return totalMarks > 0 ? formatMarks(totalMarks / 5) : 0.0;
  }

  private SaiSeniorUpperGradeDto.FirstTable buildFirstTable(
      List<LowerGradeReportCardData> marks, Student student) {

    var section = student.getSection();
    var reportCardConfigs =
        reportCardConfigRepository.findAllByOrgSlugAndBoardSlugAndGradeSlug(
            section.getOrganization(), section.getBoardSlug(), section.getGradeSlug());
    var configDetails =
        reportCardConfigs.getFirst().getReportCardConfigDetails().stream()
            .sorted(Comparator.comparing(ReportCardConfigDetail::getSeqNo))
            .toList();
    return SaiSeniorUpperGradeDto.FirstTable.builder()
        .column1(constructColumn(configDetails.getFirst(), 20))
        .column2(constructColumn(configDetails.get(1), 80))
        .column3(constructColumn(configDetails.get(2), 50))
        .subject1(
            buildFirstTableMarks(marks, List.of("English - Language", "English - Literature")))
        .subject2(buildFirstTableMarks(marks, List.of("Hindi")))
        .subject3(buildFirstTableMarks(marks, List.of("Mathematics")))
        .subject4(
            buildFirstTableMarks(
                marks, List.of("Science - Physics", "Science - Chemistry", "Science - Biology")))
        .subject5(
            buildFirstTableMarks(
                marks,
                List.of("Social Studies - History And Civics", "Social Studies - Geography")))
        .build();
  }

  private List<SaiSeniorUpperGradeDto.Marks> buildFirstTableMarks(
      List<LowerGradeReportCardData> marks, List<String> subjectFilter) {
    List<SaiSeniorUpperGradeDto.Marks> marksList = new ArrayList<>();
    var data =
        marks.stream()
            .filter(
                x ->
                    x.getCategory().equals(SubjectsCategoryEnum.SCHOLASTIC.name())
                        && x.getType().equals(SubjectsTypeEnum.MANDATORY.name())
                        && subjectFilter.contains(x.getSubjectName()))
            .sorted(Comparator.comparing(x -> subjectFilter.indexOf(x.getSubjectName())))
            .toList();

    var subjects =
        data.stream()
            .map(LowerGradeReportCardData::getSubjectName)
            .distinct()
            .filter(subjectFilter::contains)
            .toList();

    subjects.forEach(
        subject -> {
          var subjectData = data.stream().filter(x -> x.getSubjectName().equals(subject)).toList();

          var fa = buildFa(subjectData, subject);
          var sa = buildMarks(subjectData, "s.a", subject);
          var ia = buildMarks(subjectData, "i.a", subject);

          double faValue = parseOrZero(fa);
          double saValue = parseOrZero(sa);
          double iaValue = parseOrZero(ia);
          double academics = faValue + saValue;
          double percentage = faValue + saValue + iaValue;
          double overAllPercentage = percentage > 0 ? formatMarks((percentage) / 150 * 100) : 0.0;

          marksList.add(
              SaiSeniorUpperGradeDto.Marks.builder()
                  .subjectName(subject)
                  .term1SA(sa)
                  .term1IA(ia)
                  .term1FA(fa)
                  .grade(academics)
                  .percentage(overAllPercentage)
                  .build());
        });
    return sortMarks(marksList);
  }

  private List<SaiSeniorUpperGradeDto.Marks> sortMarks(
      List<SaiSeniorUpperGradeDto.Marks> marksList) {
    List<SaiSeniorUpperGradeDto.Marks> response = new ArrayList<>();

    double academicsMarks =
        marksList.stream().mapToDouble(SaiSeniorUpperGradeDto.Marks::grade).sum();
    double academicsPercentage = formatMarks(academicsMarks / marksList.size());
    double overallMarks =
        marksList.stream().mapToDouble(SaiSeniorUpperGradeDto.Marks::percentage).sum();
    double overallMarkPercentage = formatMarks(overallMarks / marksList.size());

    for (int i = 0; i < marksList.size(); i++) {
      SaiSeniorUpperGradeDto.Marks marks = marksList.get(i);

      double percentage = (i == 0) ? overallMarkPercentage : 0;
      double grade = (i == 0) ? academicsPercentage : 0;

      response.add(
          SaiSeniorUpperGradeDto.Marks.builder()
              .subjectName(marks.subjectName())
              .term1SA(marks.term1SA())
              .term1IA(marks.term1IA())
              .term1FA(marks.term1FA())
              .grade(grade)
              .percentage(percentage)
              .build());
    }

    return response;
  }

  private String buildMarks(
      List<LowerGradeReportCardData> subjectData, String assessmentSlug, String subjectName) {
    return subjectData.stream()
        .filter(
            x ->
                x.getSubjectName().equals(subjectName)
                    && x.getAssessmentSlug().equals(assessmentSlug))
        .findAny()
        .map(
            fa -> {
              if (fa.getIsAttended() == null || "false".equals(fa.getIsAttended())) {
                if (fa.getRemarks() != null) {
                  if (fa.getRemarks().contains("ML")) {
                    return "ML";
                  } else if (fa.getRemarks().contains("PA")) {
                    return "PA";
                  } else if (fa.getRemarks().contains("Absent")) {
                    return "AB";
                  }
                }
                return "AB";
              } else {
                return fa.getMarks() != null ? fa.getMarks().toString() : "-";
              }
            })
        .orElse("-");
  }

  private String buildFa(List<LowerGradeReportCardData> subjectData, String subject) {
    return subjectData.stream()
        .filter(x -> x.getSubjectName().equals(subject) && x.getAssessmentSlug().equals("f.a"))
        .map(this::determineMarkOrRemark)
        .filter(mark -> !mark.equals("AB") && !mark.equals("PA") && !mark.equals("ML"))
        .map(Double::parseDouble)
        .max(Double::compareTo)
        .map(String::valueOf)
        .orElseGet(() -> getDefaultRemark(subjectData, subject));
  }

  private String determineMarkOrRemark(LowerGradeReportCardData data) {
    if (data.getIsAttended() == null || Boolean.FALSE.toString().equals(data.getIsAttended())) {
      return getRemark(data);
    }
    return Optional.ofNullable(data.getMarks()).map(String::valueOf).orElse("0");
  }

  private String getRemark(LowerGradeReportCardData data) {
    if (data.getRemarks() == null) {
      return "AB";
    }

    String remarks = data.getRemarks();
    if (remarks.contains("ML")) {
      return "ML";
    } else if (remarks.contains("PA")) {
      return "PA";
    } else if (remarks.contains("Absent")) {
      return "AB";
    }
    return "AB";
  }

  private String getDefaultRemark(List<LowerGradeReportCardData> subjectData, String subject) {
    return subjectData.stream()
        .filter(x -> x.getSubjectName().equals(subject) && x.getAssessmentSlug().equals("f.a"))
        .map(this::getRemark)
        .findFirst()
        .orElse("AB");
  }

  private SaiSeniorUpperGradeDto.SecondTable buildSecondTable(
      List<LowerGradeReportCardData> marks) {
    var data =
        marks.stream()
            .filter(x -> x.getCategory().equals(SubjectsCategoryEnum.CO_SCHOLASTIC.name()))
            .toList();
    if (data.isEmpty()) {
      return SaiSeniorUpperGradeDto.SecondTable.builder().build();
    }
    return SaiSeniorUpperGradeDto.SecondTable.builder().marks(buildSecondTableMarks(data)).build();
  }

  private List<SaiSeniorUpperGradeDto.SecondTableMarks> buildSecondTableMarks(
      List<LowerGradeReportCardData> data) {
    List<SaiSeniorUpperGradeDto.SecondTableMarks> marksList = new ArrayList<>();
    data.forEach(
        d ->
            marksList.add(
                SaiSeniorUpperGradeDto.SecondTableMarks.builder()
                    .name(d.getSubjectName())
                    .grade(calculateGrade(d.getMarks(), d.getTotalMarks()))
                    .build()));
    return marksList;
  }

  private String calculateOverAllGrade(SaiSeniorUpperGradeDto.FirstTable firstTable) {
    boolean hasFailingMark =
        Stream.of(
                firstTable.subject1(),
                firstTable.subject2(),
                firstTable.subject3(),
                firstTable.subject4(),
                firstTable.subject5())
            .flatMap(List::stream)
            .filter(marks -> marks.percentage() != 0.0)
            .anyMatch(marks -> determinePassOrFail(marks.grade()).equals("Fail"));

    return hasFailingMark ? "Not Clear" : "Pass";
  }

  private String calculateGrade(Double marks, Double totalMarks) {
    return marks == null || marks == 0 || Objects.isNull(totalMarks)
        ? null
        : pointScaleEvaluator.evaluate("5point", BigDecimal.valueOf(((marks / totalMarks) * 100)));
  }

  private ReportCardDto.Attendance getAttendance(long studentId) {
    var term = termRepository.findBySlug("t1").orElseThrow();
    var termAssessment = termAssessmentRepository.findBySlugAndTerm("s.a", term);
    if (termAssessment.isEmpty()) {
      return ReportCardDto.Attendance.builder().build();
    }
    var studentAttendance =
        offlineTestScheduleStudentAttendanceRepository.getOfflineTestStudentAttendance(
            studentId, termAssessment.get().getId());
    return buildAttendance(studentId, studentAttendance);
  }
}
