package com.wexl.pallavi.reports;

import com.wexl.dps.DpsService;
import com.wexl.dps.reportcard.LowerGradeFirstTermReportCard;
import com.wexl.holisticreportcards.ProgressCardService;
import com.wexl.holisticreportcards.dto.ProgressCardDto;
import com.wexl.pallavi.preprimary.dto.HolisticReportCardDto;
import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.metrics.reportcards.ReportCardService;
import com.wexl.retail.model.Student;
import com.wexl.retail.model.User;
import com.wexl.retail.offlinetest.dto.LowerGradeReportDto;
import com.wexl.retail.offlinetest.dto.ReportCardDto;
import com.wexl.retail.offlinetest.model.ReportCardTemplate;
import com.wexl.retail.offlinetest.repository.LowerGradeReportCardData;
import com.wexl.retail.offlinetest.repository.OfflineTestScheduleStudentRepository;
import com.wexl.retail.offlinetest.service.pointscale.PointScaleEvaluator;
import com.wexl.retail.organization.model.Organization;
import com.wexl.retail.repository.StudentRepository;
import com.wexl.retail.student.attributes.model.StudentAttributeValueModel;
import com.wexl.retail.subjects.model.SubjectsCategoryEnum;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class HolisticReportCard extends PallaviBaseReportCardDefinition {
  private final ReportCardService reportCardService;
  private final ProgressCardService progressCardService;
  private final OfflineTestScheduleStudentRepository offlineTestScheduleStudentRepository;
  private final LowerGradeFirstTermReportCard lowerGradeFirstTermReportCard;
  private final PointScaleEvaluator pointScaleEvaluator;
  private final DpsService dpsService;
  private final StudentRepository studentRepository;
  private static final String PA1 = "pa1";
  private static final String PA2 = "pa2";
  private static final String HYE = "Half Yearly Exam";
  private static final String SE_MA = "SE/MA";
  private static final String CW_HW = "C.W/H.W";
  private static final String PEN_PAPER = "PEN PAPER TEST";
  private static final double TERM_MAX_MARKS = 100.0;
  private static final double BALAVATIKA_MARKS = 50.0;
  private static final String ORP = "Oral Reflective Practices";
  private static final String RP = "Reflective Practices-1";
  private static final String TERM1 = "TERM-1";

  private static final List<String> CATEGORIES =
      Arrays.asList("pa1", "pa2", "Half Yearly Exam", "SE/MA", "C.W/H.W", "PEN PAPER TEST");

  private static final List<String> BALAVATIKACATEGORIES =
      Arrays.asList("Oral Reflective Practices", "Reflective Practices-1", "TERM-1");

  private static final List<String> BALAVATIKA = Arrays.asList("ukg", "lkg", "nur");

  @Override
  public Optional<StudentAttributeValueModel> getStudentAttributeValue(
      Student student, String key) {
    return reportCardService.getStudentAttributeValue(student, key);
  }

  @Override
  public Map<String, Object> build(User user, Organization org, ReportCardDto.Request request) {
    var header = buildHolisticReportCardHeader(user);
    var body = buildBody(user, org.getSlug());
    return Map.of("header", header, "pages", List.of(1), "body", body);
  }

  @Override
  public Map<String, Object> buildBody(User user, Organization org, ReportCardDto.Request request) {
    return null;
  }

  @Override
  public boolean supports(ReportCardTemplate reportCardTemplate) {
    final String config = reportCardTemplate.getConfig();
    return config.equals("holistic-progress-report-card.xml");
  }

  private HolisticReportCardDto.Body buildBody(User user, String orgSlug) {
    ProgressCardDto.Response studentData;
    var student = user.getStudentInfo();
    var section = student.getSection();
    var isBalavatika = BALAVATIKA.contains(section.getGradeSlug());
    studentData = progressCardService.getProgressCardById(orgSlug, user.getAuthUserId());
    return HolisticReportCardDto.Body.builder()
        .gradeSlug(section.getGradeSlug())
        .orgSlug(orgSlug)
        .allAboutMe(buildAllAboutMe(studentData))
        .images(buildImages(studentData))
        .competencies(buildCompetencies(studentData.competenciesList()))
        .observations(buildObservations(studentData))
        .parentsFeedback(buildParentsFeedBack(studentData.parentsFeedbacks()))
        .heightAndWeight(buildHeightAndWeight(studentData))
        .selfAssessment(buildSelfAssessment(studentData.selfAssessments()))
        .peerAssessment(buildPeerAssessment(studentData.peerAssessment()))
        .academicPerformance(buildAcademicPerformance(user, isBalavatika))
        .build();
  }

  public HolisticReportCardDto.Observations buildObservations(
      ProgressCardDto.Response studentData) {

    LowerGradeReportDto.Attendance term1Remarks =
        lowerGradeFirstTermReportCard.buildAttendance(studentData.studentId());

    return HolisticReportCardDto.Observations.builder().term1(term1Remarks.remarks()).build();
  }

  private HolisticReportCardDto.AcademicPerformance buildAcademicPerformance(
      User user, boolean isBalavatika) {
    var student = user.getStudentInfo();
    var reportCardData =
        offlineTestScheduleStudentRepository.getStudentMarksByCategories(
            student.getId(), Collections.singletonList("t1"));
    return HolisticReportCardDto.AcademicPerformance.builder()
        .table1(buildTable1(reportCardData, isBalavatika))
        .table2(buildTable2(reportCardData, isBalavatika))
        .table3(buildTable3(student))
        .build();
  }

  private HolisticReportCardDto.Table3 buildTable3(Student student) {
    var user = student.getUserInfo();
    var data = dpsService.getDpsStudentAttendance(user.getExternalRef());
    return HolisticReportCardDto.Table3.builder()
        .npMarch(data.npdMarch())
        .nwMarch(data.nwdMarch())
        .npMarchAttendancePercentage(data.npMarchAttendancePercentage())
        .nwApril(data.nwdApril())
        .npApril(data.npdApril())
        .npAprilAttendancePercentage(data.npAprilAttendancePercentage())
        .nwMay(data.nwdMay())
        .npMay(data.npdMay())
        .npMayAttendancePercentage(data.npMayAttendancePercentage())
        .nwJune(data.nwdJune())
        .npJune(data.npdJune())
        .npJuneAttendancePercentage(data.npJuneAttendancePercentage())
        .nwJuly(data.nwdJuly())
        .npJuly(data.npdJuly())
        .npJulyAttendancePercentage(data.npJulyAttendancePercentage())
        .npAug(data.npdAug())
        .nwAug(data.nwdAug())
        .npAugAttendancePercentage(data.npAugAttendancePercentage())
        .nwSep(data.nwdSept())
        .npSep(data.npdSept())
        .npSepAttendancePercentage(data.npSeptAttendancePercentage())
        .nwOct(data.nwdOct())
        .npOct(data.npdOct())
        .npOctAttendancePercentage(data.npOctAttendancePercentage())
        .npNov(data.npdNov())
        .nwNov(data.nwdNov())
        .npNovAttendancePercentage(data.npNovAttendancePercentage())
        .nwDec(data.nwdDec())
        .npDec(data.npdDec())
        .npDecAttendancePercentage(data.npDecAttendancePercentage())
        .nwJan(data.nwdJan())
        .npJan(data.npdJan())
        .npJanAttendancePercentage(data.npJanAttendancePercentage())
        .nwFeb(data.nwdFeb())
        .npFeb(data.npdFeb())
        .npFebAttendancePercentage(data.npFebAttendancePercentage())
        .attendancePercentage1(data.attendancePercentage1())
        .attendancePercentage2(data.attendancePercentage2())
        .build();
  }

  private List<HolisticReportCardDto.Table2> buildTable2(
      List<LowerGradeReportCardData> reportCardData, boolean isBalavatikaGrade) {
    List<HolisticReportCardDto.Table2> marksList = new ArrayList<>();
    List<LowerGradeReportCardData> coScholasticMarks;
    coScholasticMarks =
        reportCardData.stream()
            .filter(x -> SubjectsCategoryEnum.CO_SCHOLASTIC.name().equals(x.getCategory()))
            .toList();

    coScholasticMarks.forEach(
        marks ->
            marksList.add(
                HolisticReportCardDto.Table2.builder()
                    .subjectName(marks.getSubjectName())
                    .term(buildMarks(marks, isBalavatikaGrade))
                    .build()));
    return marksList;
  }

  private String buildMarks(LowerGradeReportCardData data, boolean isBalavatikaGrade) {

    if (Boolean.FALSE.equals(Boolean.valueOf(data.getIsAttended()))) {
      return Objects.isNull(data.getRemarks())
          ? "AB"
          : data.getRemarks().substring(0, 2).toUpperCase();
    } else
      return (calculateGrade(data.getMarks(), getPointScale(isBalavatikaGrade), TERM_MAX_MARKS));
  }

  private String getPointScale(Boolean isBalavatikaGrade) {
    return Boolean.TRUE.equals(isBalavatikaGrade) ? "6point_pre_primary" : "5point-scholastic";
  }

  private List<HolisticReportCardDto.Table1> buildTable1(
      List<LowerGradeReportCardData> reportCardData, boolean isBalavatikaGrade) {
    List<HolisticReportCardDto.Table1> marksList = new ArrayList<>();

    var scholasticMarks =
        reportCardData.stream()
            .filter(x -> SubjectsCategoryEnum.SCHOLASTIC.name().equals(x.getCategory()))
            .filter(
                x -> {
                  List<String> categoriesToMatch =
                      isBalavatikaGrade ? BALAVATIKACATEGORIES : CATEGORIES;
                  return categoriesToMatch.stream()
                      .anyMatch(category -> x.getAssessmentCategory().contains(category));
                })
            .toList();

    var scholasticDataMap =
        scholasticMarks.stream()
            .collect(Collectors.groupingBy(LowerGradeReportCardData::getSubjectName));

    scholasticDataMap.forEach(
        (subject, scholasticData) -> {
          String seOrma1 = getMarks(PA1, SE_MA, scholasticData);
          String cwOrhw1 = getMarks(PA1, CW_HW, scholasticData);
          String pa1PenPaper = getMarks(PA1, PEN_PAPER, scholasticData);
          String seOrma2 = getMarks(PA2, SE_MA, scholasticData);
          String cwOrhw2 = getMarks(PA2, CW_HW, scholasticData);
          String pa2PenPaper = getMarks(PA2, PEN_PAPER, scholasticData);
          String hye = getMarks("hye", HYE, scholasticData);
          String orp = getMarks("orp", ORP, scholasticData);
          String rp = getMarks("rp", RP, scholasticData);
          String term1 = getMarks("term-1", TERM1, scholasticData);

          double total =
              Stream.of(seOrma1, cwOrhw1, pa1PenPaper, seOrma2, cwOrhw2, pa2PenPaper, hye)
                  .filter(Objects::nonNull)
                  .filter(this::isNumeric)
                  .mapToDouble(Double::parseDouble)
                  .sum();

          double rpandeterm1Total =
              Stream.of(rp, term1)
                  .filter(Objects::nonNull)
                  .filter(this::isNumeric)
                  .mapToDouble(Double::parseDouble)
                  .sum();

          double totalMarks = (isBalavatikaGrade ? BALAVATIKA_MARKS : TERM_MAX_MARKS);
          String grade =
              isBalavatikaGrade
                  ? calculateBalavatikaGrade(rpandeterm1Total, "6point_pre_primary")
                  : calculateGrade(total, "5point-scholastic", totalMarks);

          marksList.add(
              HolisticReportCardDto.Table1.builder()
                  .subjectName(subject)
                  .penAndPaper1(pa1PenPaper)
                  .cwOrhw1(cwOrhw1)
                  .seOrma1(seOrma1)
                  .penAndPaper2(pa2PenPaper)
                  .seOrma2(seOrma2)
                  .cwOrhw2(cwOrhw2)
                  .hye(hye)
                  .total(total)
                  .rpandterm1(String.format("%.2f", rpandeterm1Total))
                  .grade(grade)
                  .orp(isBalavatikaGrade ? calculateRpGrade(orp) : null)
                  .rp(rp)
                  .term1(term1)
                  .build());
        });

    return marksList;
  }

  private String calculateRpGrade(String orp) {
    if (orp == null) {
      return null;
    }
    if (orp.contains("PA") || orp.contains("ML") || orp.contains("AB")) {
      return orp;
    }
    return calculateBalavatikaGrade(Double.parseDouble(orp), "3point_pre_primary");
  }

  private boolean isNumeric(String str) {
    if (str == null || str.isEmpty()) {
      return false;
    }
    try {
      Double.parseDouble(str);
      return true;
    } catch (NumberFormatException e) {
      return false;
    }
  }

  private String calculateGrade(Double marks, String pointScale, double totalMarks) {
    return marks == null
        ? null
        : pointScaleEvaluator.evaluate(
            pointScale, BigDecimal.valueOf(((marks / totalMarks) * 100)));
  }

  private String calculateBalavatikaGrade(Double marks, String pointScale) {
    return marks == null
        ? null
        : pointScaleEvaluator.evaluate(pointScale, BigDecimal.valueOf(marks));
  }

  private String getMarks(
      String assessmentSlug, String category, List<LowerGradeReportCardData> subjectData) {
    return subjectData.stream()
        .filter(
            data ->
                assessmentSlug.equalsIgnoreCase(data.getAssessmentSlug())
                    && category.equals(data.getAssessmentCategory()))
        .map(
            data -> {
              if (data.getIsAttended() == null) {
                return null;
              } else if (Boolean.FALSE.equals(Boolean.valueOf(data.getIsAttended()))) {
                return Objects.isNull(data.getRemarks())
                    ? "AB"
                    : data.getRemarks().substring(0, 2).toUpperCase();
              }
              if (data.getMarks() == null) {
                return null;
              }
              double roundedMarks = Math.ceil(data.getMarks() * 10) / 10.0;
              return String.valueOf(roundedMarks);
            })
        .filter(Objects::nonNull)
        .findFirst()
        .orElse(null);
  }

  private List<HolisticReportCardDto.PeerAssessment> buildPeerAssessment(
      List<ProgressCardDto.PeerAssessment> peerAssessments) {
    List<HolisticReportCardDto.PeerAssessment> responseList = new ArrayList<>();
    peerAssessments.forEach(
        assessment ->
            responseList.add(
                HolisticReportCardDto.PeerAssessment.builder()
                    .id(assessment.id())
                    .name(assessment.name())
                    .term1(assessment.term1() == null ? null : assessment.term1().ordinal())
                    .build()));
    return responseList;
  }

  private List<HolisticReportCardDto.SelfAssessment> buildSelfAssessment(
      List<ProgressCardDto.SelfAssessment> selfAssessments) {
    List<HolisticReportCardDto.SelfAssessment> responseList = new ArrayList<>();
    selfAssessments.forEach(
        assessment ->
            responseList.add(
                HolisticReportCardDto.SelfAssessment.builder()
                    .id(assessment.id())
                    .name(assessment.name())
                    .term1(assessment.term1())
                    .build()));
    return responseList;
  }

  private HolisticReportCardDto.HeightAndWeight buildHeightAndWeight(
      ProgressCardDto.Response studentData) {
    return HolisticReportCardDto.HeightAndWeight.builder()
        .term1Height(studentData.term1Height())
        .term1Weight(studentData.term1Weight())
        .build();
  }

  private List<HolisticReportCardDto.ParentsFeedback> buildParentsFeedBack(
      List<ProgressCardDto.ParentsFeedback> parentsFeedbacks) {
    List<HolisticReportCardDto.ParentsFeedback> responseList = new ArrayList<>();
    parentsFeedbacks.forEach(
        feedback ->
            responseList.add(
                HolisticReportCardDto.ParentsFeedback.builder()
                    .id(feedback.id())
                    .name(feedback.name())
                    .term1(feedback.term1())
                    .build()));
    return responseList;
  }

  public List<HolisticReportCardDto.Competencies> buildCompetencies(
      List<ProgressCardDto.Competencies> competencies) {
    List<HolisticReportCardDto.Competencies> responseList = new ArrayList<>();
    competencies.forEach(
        comp -> {
          var skills =
              comp.skills().stream()
                  .map(
                      skill ->
                          HolisticReportCardDto.Skill.builder()
                              .skillName(skill.skillName())
                              .details(buildDetails(skill.competencyDetails()))
                              .build())
                  .toList();

          responseList.add(
              HolisticReportCardDto.Competencies.builder()
                  .subjectSlug(comp.subjectSlug())
                  .subjectName(comp.subjectName())
                  .skills(skills)
                  .build());
        });
    return responseList;
  }

  private List<HolisticReportCardDto.Details> buildDetails(List<ProgressCardDto.Details> details) {
    List<HolisticReportCardDto.Details> responseList = new ArrayList<>();
    details.forEach(
        detail ->
            responseList.add(
                HolisticReportCardDto.Details.builder()
                    .id(detail.id())
                    .subjectValue(detail.name())
                    .term1Value(detail.term1())
                    .term2Value(detail.term2())
                    .build()));
    return responseList;
  }

  private HolisticReportCardDto.Images buildImages(ProgressCardDto.Response studentData) {

    return HolisticReportCardDto.Images.builder()
        .aGlimpseOfMyFamily(studentData.aGlimpseOfMyFamily())
        .aGlimpseOfMySelf(studentData.aGlimpseOfMySelf())
        .learnersPortFolio(studentData.learnersPortFolio())
        .build();
  }

  private HolisticReportCardDto.AllAboutMe buildAllAboutMe(ProgressCardDto.Response studentData) {
    var student =
        studentRepository
            .findById(studentData.studentId())
            .orElseThrow(
                () ->
                    new ApiException(InternalErrorCodes.NO_RECORD_FOUND, "error.StudentNotFound"));
    Optional<StudentAttributeValueModel> mother =
        reportCardService.getStudentAttributeValue(student, "mother_name");
    Optional<StudentAttributeValueModel> father =
        reportCardService.getStudentAttributeValue(student, "father_name");
    Optional<StudentAttributeValueModel> dateOfBirth =
        reportCardService.getStudentAttributeValue(student, "date_of_birth");

    return HolisticReportCardDto.AllAboutMe.builder()
        .studentName(studentData.studentName())
        .classTeacher(studentData.classTeacher())
        .dateOfBirth(dateOfBirth.map(StudentAttributeValueModel::getValue).orElse(null))
        .admissionNo(studentData.admissionNo())
        .iLiveIn(studentData.iLiveIn())
        .fatherPhoneNo(studentData.fatherPhoneNo())
        .address(studentData.address())
        .gradeName(studentData.gradeName())
        .gradeSlug(studentData.gradeSlug())
        .motherName(mother.map(StudentAttributeValueModel::getValue).orElse(null))
        .fatherName(father.map(StudentAttributeValueModel::getValue).orElse(null))
        .motherPhoneNo(studentData.motherPhoneNo())
        .fatherPhoneNo(student.getUserInfo().getMobileNumber())
        .sectionName(studentData.sectionName())
        .myFavouriteColoursAre(studentData.myFavouriteColoursAre())
        .myFavouriteFoods(studentData.myFavouriteFoods())
        .myFavouriteGames(studentData.myFavouriteGames())
        .thingsILike(studentData.thingsILike())
        .myFriendsAre(studentData.myFriendsAre())
        .myFavouriteAnimals(studentData.myFavouriteAnimals())
        .build();
  }

  private HolisticReportCardDto.Header buildHolisticReportCardHeader(User user) {
    var student = user.getStudentInfo();
    String subjectTitle = null;
    if (student.getSection().getGradeSlug().equals("i")) {
      subjectTitle = "GRADE - 1";
    } else if (student.getSection().getGradeSlug().equals("ii")) {
      subjectTitle = "GRADE - 2";
    } else if (student.getSection().getGradeSlug().equals("nur")) {
      subjectTitle = "BALVATIKA - 1";
    } else if (student.getSection().getGradeSlug().equals("lkg")) {
      subjectTitle = "BALVATIKA - 2";
    } else if (student.getSection().getGradeSlug().equals("ukg")) {
      subjectTitle = "BALVATIKA - 3";
    }
    return HolisticReportCardDto.Header.builder()
        .reportName("HOLISTIC PROGRESS REPORT CARD 2024-2025")
        .subjectTtl(subjectTitle)
        .build();
  }
}
