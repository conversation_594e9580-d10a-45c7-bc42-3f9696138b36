package com.wexl.pallavi.reports;

import com.wexl.dps.DpsService;
import com.wexl.holisticreportcards.ProgressCardService;
import com.wexl.holisticreportcards.dto.ProgressCardDto;
import com.wexl.pallavi.preprimary.dto.HolisticReportCardDto;
import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.metrics.reportcards.ReportCardService;
import com.wexl.retail.model.Student;
import com.wexl.retail.model.User;
import com.wexl.retail.offlinetest.dto.LowerGradeReportDto;
import com.wexl.retail.offlinetest.dto.ReportCardDto;
import com.wexl.retail.offlinetest.model.ReportCardTemplate;
import com.wexl.retail.offlinetest.repository.LowerGradeReportCardData;
import com.wexl.retail.offlinetest.repository.OfflineTestScheduleStudentAttendanceRepository;
import com.wexl.retail.offlinetest.repository.OfflineTestScheduleStudentRepository;
import com.wexl.retail.offlinetest.service.pointscale.PointScaleEvaluator;
import com.wexl.retail.organization.model.Organization;
import com.wexl.retail.reportcards.dto.ReportCardConfigDto;
import com.wexl.retail.repository.StudentRepository;
import com.wexl.retail.student.attributes.model.StudentAttributeValueModel;
import com.wexl.retail.subjects.model.SubjectsCategoryEnum;
import com.wexl.retail.subjects.repository.SubjectsMetaDataRepository;
import com.wexl.retail.telegram.service.UserService;
import com.wexl.saisenior.reportcard.repository.SaiSeniorRepository;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class OverAllHolisticReportCard extends PallaviBaseReportCardDefinition {

  private final List<String> terms = List.of("t1", "t2");
  private final ReportCardService reportCardService;
  private final SaiSeniorRepository saiSeniorRepository;
  private final ProgressCardService progressCardService;
  private final OfflineTestScheduleStudentRepository offlineTestScheduleStudentRepository;
  private final OfflineTestScheduleStudentAttendanceRepository
      offlineTestScheduleStudentAttendanceRepository;
  private final PointScaleEvaluator pointScaleEvaluator;
  private final StudentRepository studentRepository;
  private final HolisticReportCard holisticReportCard;
  private final DpsService dpsService;
  private final UserService userService;
  private final SubjectsMetaDataRepository subjectsMetaDataRepository;
  private static final String PA1 = "pa1";
  private static final String PA2 = "pa2";
  private static final String PA3 = "pa3";
  private static final String PA4 = "pa4";
  private static final String HYE = "Half Yearly Exam";
  private static final String YE = "FINAL EXAM";
  private static final String SE_MA = "SE/MA";
  private static final String CW_HW = "C.W/H.W";
  private static final String PEN_PAPER = "PEN PAPER TEST";
  private static final double TERM_MAX_MARKS = 100.0;
  private static final double BALAVATIKA_MARKS = 50.0;
  private static final String ORP = "Oral Reflective Practices";
  private static final String ORP2 = "Oral Reflective Practices-2";
  private static final String RP = "Reflective Practices-1";
  private static final String RP2 = "Reflective Practices-2";
  private static final String TERM1 = "TERM-1";
  private static final String TERM2 = "TERM-2";

  private static final List<String> CATEGORIES =
      Arrays.asList("pa1", "pa2", "Half Yearly Exam", "SE/MA", "C.W/H.W", "PEN PAPER TEST");
  private static final List<String> CATEGORIEST2 =
      Arrays.asList("pa3", "pa4", "FINAL EXAM", "SE/MA", "C.W/H.W", "PEN PAPER TEST");

  private static final List<String> NEWBALAVATIKACATEGORIES =
      Arrays.asList(
          "Oral Reflective Practices",
          "Reflective Practices-1",
          "TERM-1",
          "Oral Reflective Practices-2",
          "Reflective Practices-2",
          "TERM-2");

  private static final List<String> BALAVATIKACATEGORIES =
      Arrays.asList("orp", "rp", "term-1", "term-2");

  private static final List<String> BALAVATIKACATEGORIEST2 =
      Arrays.asList("orp2", "rp2", "orp", "rp", "term-1", "term-2");

  private static final List<String> BALAVATIKA = Arrays.asList("ukg", "lkg", "nur");

  @Override
  public Optional<StudentAttributeValueModel> getStudentAttributeValue(
      Student student, String key) {
    return reportCardService.getStudentAttributeValue(student, key);
  }

  @Override
  public Map<String, Object> build(User user, Organization org, ReportCardDto.Request request) {
    var header = buildHolisticReportCardHeader(user);
    var body = buildBody(user, org.getSlug());
    return Map.of("header", header, "pages", List.of(1), "body", body);
  }

  @Override
  public Map<String, Object> buildBody(User user, Organization org, ReportCardDto.Request request) {
    return null;
  }

  @Override
  public boolean supports(ReportCardTemplate reportCardTemplate) {
    final String config = reportCardTemplate.getConfig();
    return config.equals("overall-holistic-progress-report-card.xml");
  }

  private HolisticReportCardDto.Body buildBody(User user, String orgSlug) {
    ProgressCardDto.Response studentData;
    var student = user.getStudentInfo();
    var section = student.getSection();
    var isBalavatika = BALAVATIKA.contains(section.getGradeSlug());
    studentData = progressCardService.getProgressCardById(orgSlug, user.getAuthUserId());
    return HolisticReportCardDto.Body.builder()
        .orgSlug(orgSlug)
        .gradeSlug(section.getGradeSlug())
        .allAboutMe(buildAllAboutMe(studentData))
        .images(buildImages(studentData))
        .competencies(holisticReportCard.buildCompetencies(studentData.competenciesList()))
        .observations(buildObservations(studentData))
        .parentsFeedback(buildParentsFeedBack(studentData.parentsFeedbacks()))
        .heightAndWeight(buildHeightAndWeight(studentData))
        .selfAssessment(buildSelfAssessment(studentData.selfAssessments()))
        .peerAssessment(buildPeerAssessment(studentData.peerAssessment()))
        .academicPerformance(buildAcademicPerformance(user, isBalavatika))
        .build();
  }

  private HolisticReportCardDto.Observations buildObservations(
      ProgressCardDto.Response studentData) {
    LowerGradeReportDto.Attendance term2Remarks =
        buildStudentAttendance(studentData.studentId(), terms.getLast());
    LowerGradeReportDto.Attendance term1Remarks =
        buildStudentAttendance(studentData.studentId(), terms.getFirst());

    return HolisticReportCardDto.Observations.builder()
        .term1(term1Remarks.remarks())
        .term2(term2Remarks.remarks())
        .build();
  }

  private LowerGradeReportDto.Attendance buildStudentAttendance(Long studentId, String termSlug) {
    var studentAttendances =
        offlineTestScheduleStudentAttendanceRepository.getStudentAttendanceByStudentIdAndTermSlug(
            studentId, termSlug);
    if (!studentAttendances.isEmpty()) {
      var studentAttendance =
          studentAttendances.stream().filter(x -> Objects.nonNull(x.getRemarks())).toList();
      if (!studentAttendance.isEmpty()) {
        return LowerGradeReportDto.Attendance.builder()
            .remarks(studentAttendance.getFirst().getRemarks())
            .build();
      }
    }
    return LowerGradeReportDto.Attendance.builder().build();
  }

  private HolisticReportCardDto.AcademicPerformance buildAcademicPerformance(
      User user, boolean isBalavatika) {
    var student = user.getStudentInfo();

    var marks =
        saiSeniorRepository.getStudentT2ReportByStudentAndAssessments(
            student.getId(), student.getSection().getGradeSlug(), terms);
    if (Objects.isNull(marks) || marks.isEmpty()) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "Report has not been generated");
    }

    var table1 = buildTable1(isBalavatika, student.getId());
    return HolisticReportCardDto.AcademicPerformance.builder()
        .table1(table1)
        .table2(buildTable2(marks, isBalavatika))
        .table3(buildTable3(student))
        .table4(buildTable4(isBalavatika, student.getId(), table1))
        .build();
  }

  private HolisticReportCardDto.Table3 buildTable3(Student student) {
    var user = student.getUserInfo();
    var data = dpsService.getDpsStudentAttendance(user.getExternalRef());
    Long attendance1 =
        (long)
            Stream.of(
                    data.npMarchAttendancePercentage(),
                    data.npAprilAttendancePercentage(),
                    data.npJuneAttendancePercentage(),
                    data.npJulyAttendancePercentage(),
                    data.npAugAttendancePercentage(),
                    data.npSeptAttendancePercentage())
                .filter(Objects::nonNull)
                .mapToLong(Long::longValue)
                .average()
                .orElse(0);

    Long attendance2 =
        (long)
            Stream.of(
                    data.npOctAttendancePercentage(),
                    data.npNovAttendancePercentage(),
                    data.npDecAttendancePercentage(),
                    data.npJanAttendancePercentage(),
                    data.npFebAttendancePercentage())
                .filter(Objects::nonNull)
                .mapToLong(Long::longValue)
                .average()
                .orElse(0);

    return HolisticReportCardDto.Table3.builder()
        .npMarch(data.npdMarch())
        .nwMarch(data.nwdMarch())
        .npMarchAttendancePercentage(data.npMarchAttendancePercentage())
        .nwApril(data.nwdApril())
        .npApril(data.npdApril())
        .npAprilAttendancePercentage(data.npAprilAttendancePercentage())
        .nwMay(data.nwdMay())
        .npMay(data.npdMay())
        .npMayAttendancePercentage(data.npMayAttendancePercentage())
        .nwJune(data.nwdJune())
        .npJune(data.npdJune())
        .npJuneAttendancePercentage(data.npJuneAttendancePercentage())
        .nwJuly(data.nwdJuly())
        .npJuly(data.npdJuly())
        .npJulyAttendancePercentage(data.npJulyAttendancePercentage())
        .npAug(data.npdAug())
        .nwAug(data.nwdAug())
        .npAugAttendancePercentage(data.npAugAttendancePercentage())
        .nwSep(data.nwdSept())
        .npSep(data.npdSept())
        .npSepAttendancePercentage(data.npSeptAttendancePercentage())
        .nwOct(data.nwdOct())
        .npOct(data.npdOct())
        .npOctAttendancePercentage(data.npOctAttendancePercentage())
        .npNov(data.npdNov())
        .nwNov(data.nwdNov())
        .npNovAttendancePercentage(data.npNovAttendancePercentage())
        .nwDec(data.nwdDec())
        .npDec(data.npdDec())
        .npDecAttendancePercentage(data.npDecAttendancePercentage())
        .nwJan(data.nwdJan())
        .npJan(data.npdJan())
        .npJanAttendancePercentage(data.npJanAttendancePercentage())
        .nwFeb(data.nwdFeb())
        .npFeb(data.npdFeb())
        .npFebAttendancePercentage(data.npFebAttendancePercentage())
        .attendancePercentage1(data.attendancePercentage1())
        .attendancePercentage2(data.attendancePercentage2())
        .overallPercentage1(attendance1)
        .overallPercentage2(attendance2)
        .build();
  }

  private List<HolisticReportCardDto.Table2> buildTable2(
      List<LowerGradeReportCardData> reportCardData, boolean isBalavatikaGrade) {
    List<HolisticReportCardDto.Table2> marksList = new ArrayList<>();
    List<LowerGradeReportCardData> coScholasticMarks;
    coScholasticMarks =
        reportCardData.stream()
            .filter(x -> SubjectsCategoryEnum.CO_SCHOLASTIC.name().equals(x.getCategory()))
            .toList();

    Map<String, HolisticReportCardDto.Table2> recordMap = new HashMap<>();

    coScholasticMarks.forEach(
        marks -> {
          String termValue = buildMarks(marks, isBalavatikaGrade);

          boolean isTerm2 = "t2".equals(marks.getTermSlug());

          HolisticReportCardDto.Table2 existing =
              recordMap.getOrDefault(
                  marks.getSubjectName(),
                  HolisticReportCardDto.Table2.builder()
                      .subjectName(marks.getSubjectName())
                      .term(null)
                      .term2(null)
                      .build());

          HolisticReportCardDto.Table2 updated =
              HolisticReportCardDto.Table2.builder()
                  .subjectName(existing.subjectName())
                  .term(isTerm2 ? existing.term() : termValue)
                  .term2(isTerm2 ? termValue : existing.term2())
                  .build();

          recordMap.put(marks.getSubjectName(), updated);
        });
    marksList.addAll(recordMap.values());

    return marksList;
  }

  private String buildMarks(LowerGradeReportCardData data, boolean isBalavatikaGrade) {

    if (Boolean.FALSE.equals(Boolean.valueOf(data.getIsAttended()))) {
      return Objects.isNull(data.getRemarks())
          ? "AB"
          : data.getRemarks().substring(0, 2).toUpperCase();
    } else
      return (calculateGrade(data.getMarks(), getPointScale(isBalavatikaGrade), TERM_MAX_MARKS));
  }

  private String getPointScale(Boolean isBalavatikaGrade) {
    return Boolean.TRUE.equals(isBalavatikaGrade) ? "6point_pre_primary" : "5point-scholastic";
  }

  private List<HolisticReportCardDto.Table1> buildTable4(
      boolean isBalavatikaGrade, long studentId, List<HolisticReportCardDto.Table1> table1) {
    List<HolisticReportCardDto.Table1> marksList = new ArrayList<>();

    var reportCardData =
        offlineTestScheduleStudentRepository.getStudentMarksByCategories(
            studentId, Collections.singletonList("t2"));

    var scholasticMarks =
        reportCardData.stream()
            .filter(x -> SubjectsCategoryEnum.SCHOLASTIC.name().equals(x.getCategory()))
            .filter(
                x -> {
                  List<String> categoriesToMatch =
                      isBalavatikaGrade ? BALAVATIKACATEGORIEST2 : CATEGORIEST2;
                  return categoriesToMatch.stream()
                      .anyMatch(category -> x.getAssessmentCategory().contains(category));
                })
            .toList();

    var scholasticDataMap =
        scholasticMarks.stream()
            .collect(Collectors.groupingBy(LowerGradeReportCardData::getSubjectName));

    scholasticDataMap.forEach(
        (subject, scholasticData) -> {
          String seOrma1 = getMarks(PA3, SE_MA, scholasticData);
          String cwOrhw1 = getMarks(PA3, CW_HW, scholasticData);
          String pa1PenPaper = getMarks(PA3, PEN_PAPER, scholasticData);
          String seOrma2 = getMarks(PA4, SE_MA, scholasticData);
          String cwOrhw2 = getMarks(PA4, CW_HW, scholasticData);
          String pa2PenPaper = getMarks(PA4, PEN_PAPER, scholasticData);
          String hye = getMarks("ye", YE, scholasticData);
          String orp = getMarks("orp", ORP, scholasticData);
          String rp = getMarks("rp", RP, scholasticData);
          String term1 = getMarks("term-1", TERM1, scholasticData);

          double total =
              Stream.of(seOrma1, cwOrhw1, pa1PenPaper, seOrma2, cwOrhw2, pa2PenPaper, hye)
                  .filter(Objects::nonNull)
                  .filter(this::isNumeric)
                  .mapToDouble(Double::parseDouble)
                  .sum();

          double rpandeterm1Total =
              Stream.of(rp, term1)
                  .filter(Objects::nonNull)
                  .filter(this::isNumeric)
                  .mapToDouble(Double::parseDouble)
                  .sum();

          double totalMarks = (isBalavatikaGrade ? BALAVATIKA_MARKS : TERM_MAX_MARKS);
          String grade =
              isBalavatikaGrade
                  ? calculateBalavatikaGrade(rpandeterm1Total, "6point_pre_primary")
                  : calculateGrade(total, "5point-scholastic", totalMarks);

          var table1Data = table1.stream().filter(x -> x.subjectName().equals(subject)).findFirst();
          double overallPercentage = 0;
          String overAllGrade = null;
          if (table1Data.isPresent()) {
            var table1Percentage = table1Data.get().total();
            overallPercentage = (table1Percentage + total) / 2;
            overAllGrade =
                isBalavatikaGrade
                    ? calculateBalavatikaGrade(overallPercentage, "6point_pre_primary")
                    : calculateBalavatikaGrade(overallPercentage, "5point-scholastic");
          }
          var subjectData =
              scholasticMarks.stream().filter(x -> x.getSubjectName().equals(subject)).toList();
          marksList.add(
              HolisticReportCardDto.Table1.builder()
                  .subjectName(subject)
                  .subjectSlug(subjectData.get(0).getSubjectSlug())
                  .penAndPaper1(pa1PenPaper)
                  .cwOrhw1(cwOrhw1)
                  .seOrma1(seOrma1)
                  .penAndPaper2(pa2PenPaper)
                  .seOrma2(seOrma2)
                  .cwOrhw2(cwOrhw2)
                  .hye(hye)
                  .total(total)
                  .rpandterm1(String.format("%.2f", rpandeterm1Total))
                  .grade(grade)
                  .orp(isBalavatikaGrade ? calculateRpGrade(orp) : null)
                  .rp(rp)
                  .overallTerm1AndTerm2Percentage(formatMarks(overallPercentage))
                  .overallGrading(overAllGrade)
                  .term1(term1)
                  .build());
        });

    return marksList;
  }

  private List<HolisticReportCardDto.Table1> buildTable1(
      boolean isBalavatikaGrade, long studentId) {
    List<HolisticReportCardDto.Table1> marksList = new ArrayList<>();

    var reportCardData =
        offlineTestScheduleStudentRepository.getStudentMarksByCategories(studentId, terms);
    var scholasticMarks =
        reportCardData.stream()
            .filter(x -> SubjectsCategoryEnum.SCHOLASTIC.name().equals(x.getCategory()))
            .filter(
                x -> {
                  List<String> categoriesToMatch =
                      isBalavatikaGrade ? NEWBALAVATIKACATEGORIES : CATEGORIES;
                  return categoriesToMatch.stream()
                      .anyMatch(category -> x.getAssessmentCategory().contains(category));
                })
            .toList();

    var scholasticDataMap =
        scholasticMarks.stream()
            .collect(Collectors.groupingBy(LowerGradeReportCardData::getSubjectName));

    scholasticDataMap.forEach(
        (subject, scholasticData) -> {
          String seOrma1 = getMarks(PA1, SE_MA, scholasticData);
          String cwOrhw1 = getMarks(PA1, CW_HW, scholasticData);
          String pa1PenPaper = getMarks(PA1, PEN_PAPER, scholasticData);
          String seOrma2 = getMarks(PA2, SE_MA, scholasticData);
          String cwOrhw2 = getMarks(PA2, CW_HW, scholasticData);
          String pa2PenPaper = getMarks(PA2, PEN_PAPER, scholasticData);
          String hye = getMarks("hye", HYE, scholasticData);
          String orp = getMarks("orp", ORP, scholasticData);
          String orp2 = getMarks("orp2", ORP2, scholasticData);
          String rp = getMarks("rp", RP, scholasticData);
          String rp2 = getMarks("rp2", RP2, scholasticData);
          String term1 = getMarks("term-1", TERM1, scholasticData);
          String term2 = getMarks("term-2", TERM2, scholasticData);

          double total =
              Stream.of(seOrma1, cwOrhw1, pa1PenPaper, seOrma2, cwOrhw2, pa2PenPaper, hye)
                  .filter(Objects::nonNull)
                  .filter(this::isNumeric)
                  .mapToDouble(Double::parseDouble)
                  .sum();

          double rpandeTerm1Total =
              Stream.of(rp, term1)
                  .filter(Objects::nonNull)
                  .filter(this::isNumeric)
                  .mapToDouble(Double::parseDouble)
                  .sum();
          double rpandeTerm2Total =
              Stream.of(rp2, term2)
                  .filter(Objects::nonNull)
                  .filter(this::isNumeric)
                  .mapToDouble(Double::parseDouble)
                  .sum();

          double totalMarks = (isBalavatikaGrade ? BALAVATIKA_MARKS : TERM_MAX_MARKS);
          var t1t2Total = rpandeTerm1Total + rpandeTerm2Total;
          String gradingSystem = isBalavatikaGrade ? "6point_pre_primary" : "5point-scholastic";
          String t1Grade =
              isBalavatikaGrade
                  ? calculateBalavatikaGrade(rpandeTerm1Total, gradingSystem)
                  : calculateGrade(total, gradingSystem, totalMarks);
          String t2Grade =
              isBalavatikaGrade
                  ? calculateBalavatikaGrade(rpandeTerm2Total, gradingSystem)
                  : calculateGrade(total, gradingSystem, totalMarks);
          String overAllgrade =
              isBalavatikaGrade
                  ? calculateBalavatikaGrade(t1t2Total / 2, gradingSystem)
                  : calculateGrade(total, gradingSystem, totalMarks);

          var subjectData =
              scholasticMarks.stream().filter(x -> x.getSubjectName().equals(subject)).toList();
          marksList.add(
              HolisticReportCardDto.Table1.builder()
                  .subjectName(subject)
                  .subjectSlug(subjectData.get(0).getSubjectSlug())
                  .penAndPaper1(pa1PenPaper)
                  .cwOrhw1(cwOrhw1)
                  .seOrma1(seOrma1)
                  .penAndPaper2(pa2PenPaper)
                  .seOrma2(seOrma2)
                  .cwOrhw2(cwOrhw2)
                  .hye(hye)
                  .total(total)
                  .rpandterm1(String.format("%.2f", rpandeTerm1Total))
                  .grade(t1Grade)
                  .orp(isBalavatikaGrade ? calculateRpGrade(orp) : null)
                  .rp(rp)
                  .orpTerm2(isBalavatikaGrade ? calculateRpGrade(orp2) : null)
                  .rpTerm2(rp2)
                  .rpandterm2(String.format("%.2f", rpandeTerm2Total))
                  .gradeTerm2(t2Grade)
                  .term1(term1)
                  .term2(term2)
                  .term1AndTerm2(String.format("%.2f", t1t2Total))
                  .term1AndTerm2Grade(overAllgrade)
                  .build());
        });

    return marksList;
  }

  private String getMarks(
      String assessmentSlug, String category, List<LowerGradeReportCardData> subjectData) {
    return subjectData.stream()
        .filter(
            data ->
                assessmentSlug.equalsIgnoreCase(data.getAssessmentSlug())
                    && category.equals(data.getAssessmentCategory()))
        .map(
            data -> {
              if (data.getIsAttended() == null) {
                return null;
              } else if (Boolean.FALSE.equals(Boolean.valueOf(data.getIsAttended()))) {
                return Objects.isNull(data.getRemarks())
                    ? "AB"
                    : data.getRemarks().substring(0, 2).toUpperCase();
              }
              if (data.getMarks() == null) {
                return null;
              }
              double marks =
                  assessmentSlug.equals("hye") || assessmentSlug.equals("ye")
                      ? ((data.getMarks() / data.getTotalMarks()) * 50)
                      : data.getMarks() * 10 / 10.0;
              return String.format("%.2f", marks);
            })
        .filter(Objects::nonNull)
        .findFirst()
        .orElse(null);
  }

  private String calculateRpGrade(String orp) {
    if (orp == null) {
      return null;
    }
    if (orp.contains("PA") || orp.contains("ML") || orp.contains("AB") || orp.contains("PL")) {
      return orp;
    }
    return calculateBalavatikaGrade(Double.parseDouble(orp), "3point_pre_primary");
  }

  private boolean isNumeric(String str) {
    if (str == null || str.isEmpty()) {
      return false;
    }
    try {
      Double.parseDouble(str);
      return true;
    } catch (NumberFormatException e) {
      return false;
    }
  }

  private String calculateGrade(Double marks, String pointScale, double totalMarks) {
    return marks == null
        ? null
        : pointScaleEvaluator.evaluate(
            pointScale, BigDecimal.valueOf(((marks / totalMarks) * 100)));
  }

  private String calculateBalavatikaGrade(Double marks, String pointScale) {
    return marks == null
        ? null
        : pointScaleEvaluator.evaluate(pointScale, BigDecimal.valueOf(marks));
  }

  private List<HolisticReportCardDto.PeerAssessment> buildPeerAssessment(
      List<ProgressCardDto.PeerAssessment> peerAssessments) {
    List<HolisticReportCardDto.PeerAssessment> responseList = new ArrayList<>();
    peerAssessments.forEach(
        assessment ->
            responseList.add(
                HolisticReportCardDto.PeerAssessment.builder()
                    .id(assessment.id())
                    .name(assessment.name())
                    .term1(assessment.term1() == null ? null : assessment.term1().ordinal())
                    .term2(assessment.term2() == null ? null : assessment.term2().ordinal())
                    .build()));
    return responseList;
  }

  private List<HolisticReportCardDto.SelfAssessment> buildSelfAssessment(
      List<ProgressCardDto.SelfAssessment> selfAssessments) {
    List<HolisticReportCardDto.SelfAssessment> responseList = new ArrayList<>();
    selfAssessments.forEach(
        assessment ->
            responseList.add(
                HolisticReportCardDto.SelfAssessment.builder()
                    .id(assessment.id())
                    .name(assessment.name())
                    .term1(assessment.term1())
                    .term2(assessment.term2())
                    .build()));
    return responseList;
  }

  private HolisticReportCardDto.HeightAndWeight buildHeightAndWeight(
      ProgressCardDto.Response studentData) {
    return HolisticReportCardDto.HeightAndWeight.builder()
        .term1Height(studentData.term1Height())
        .term1Weight(studentData.term1Weight())
        .term2Height(studentData.term2Height())
        .term2Weight(studentData.term2Weight())
        .build();
  }

  private List<HolisticReportCardDto.ParentsFeedback> buildParentsFeedBack(
      List<ProgressCardDto.ParentsFeedback> parentsFeedbacks) {
    List<HolisticReportCardDto.ParentsFeedback> responseList = new ArrayList<>();
    parentsFeedbacks.forEach(
        feedback ->
            responseList.add(
                HolisticReportCardDto.ParentsFeedback.builder()
                    .id(feedback.id())
                    .name(feedback.name())
                    .term1(feedback.term1())
                    .term2(feedback.term2())
                    .build()));
    return responseList;
  }

  private HolisticReportCardDto.Images buildImages(ProgressCardDto.Response studentData) {

    return HolisticReportCardDto.Images.builder()
        .aGlimpseOfMyFamily(studentData.aGlimpseOfMyFamily())
        .aGlimpseOfMySelf(studentData.aGlimpseOfMySelf())
        .learnersPortFolio(studentData.learnersPortFolio())
        .build();
  }

  private HolisticReportCardDto.AllAboutMe buildAllAboutMe(ProgressCardDto.Response studentData) {
    var student =
        studentRepository
            .findById(studentData.studentId())
            .orElseThrow(
                () ->
                    new ApiException(InternalErrorCodes.NO_RECORD_FOUND, "error.StudentNotFound"));
    Optional<StudentAttributeValueModel> mother =
        reportCardService.getStudentAttributeValue(student, "mother_name");
    Optional<StudentAttributeValueModel> father =
        reportCardService.getStudentAttributeValue(student, "father_name");
    Optional<StudentAttributeValueModel> dateOfBirth =
        reportCardService.getStudentAttributeValue(student, "date_of_birth");

    return HolisticReportCardDto.AllAboutMe.builder()
        .studentName(studentData.studentName())
        .classTeacher(studentData.classTeacher())
        .dateOfBirth(dateOfBirth.map(StudentAttributeValueModel::getValue).orElse(null))
        .admissionNo(studentData.admissionNo())
        .iLiveIn(studentData.iLiveIn())
        .fatherPhoneNo(studentData.fatherPhoneNo())
        .address(studentData.address())
        .gradeName(studentData.gradeName())
        .gradeSlug(studentData.gradeSlug())
        .motherName(mother.map(StudentAttributeValueModel::getValue).orElse(null))
        .fatherName(father.map(StudentAttributeValueModel::getValue).orElse(null))
        .motherPhoneNo(studentData.motherPhoneNo())
        .fatherPhoneNo(student.getUserInfo().getMobileNumber())
        .sectionName(studentData.sectionName())
        .myFavouriteColoursAre(studentData.myFavouriteColoursAre())
        .myFavouriteFoods(studentData.myFavouriteFoods())
        .myFavouriteGames(studentData.myFavouriteGames())
        .thingsILike(studentData.thingsILike())
        .myFriendsAre(studentData.myFriendsAre())
        .myFavouriteAnimals(studentData.myFavouriteAnimals())
        .build();
  }

  private HolisticReportCardDto.Header buildHolisticReportCardHeader(User user) {
    var student = user.getStudentInfo();
    String subjectTitle = null;
    if (student.getSection().getGradeSlug().equals("i")) {
      subjectTitle = "GRADE - 1";
    } else if (student.getSection().getGradeSlug().equals("ii")) {
      subjectTitle = "GRADE - 2";
    } else if (student.getSection().getGradeSlug().equals("nur")) {
      subjectTitle = "BALVATIKA - 1";
    } else if (student.getSection().getGradeSlug().equals("lkg")) {
      subjectTitle = "BALVATIKA - 2";
    } else if (student.getSection().getGradeSlug().equals("ukg")) {
      subjectTitle = "BALVATIKA - 3";
    }
    return HolisticReportCardDto.Header.builder()
        .reportName("HOLISTIC PROGRESS REPORT CARD 2024-2025")
        .subjectTtl(subjectTitle)
        .build();
  }

  public List<ReportCardConfigDto.GradeAndPercentageResponse> getAGradeResponse(
      List<Student> students, Boolean aFalse, String gradeSlug) {
    List<ReportCardConfigDto.GradeAndPercentageResponse> responseList = new ArrayList<>();
    students.forEach(
        student -> {
          User user = student.getUserInfo();
          try {
            HolisticReportCardDto.AcademicPerformance academicPerformance =
                buildAcademicPerformance(user, false);
            List<HolisticReportCardDto.Table1> table = academicPerformance.table4();
            double totalPercentage =
                table.stream()
                    .mapToDouble(HolisticReportCardDto.Table1::overallTerm1AndTerm2Percentage)
                    .sum();
            double overallPercentage = totalPercentage / table.size();
            String overallGrade = calculateBalavatikaGrade(overallPercentage, "5point-scholastic");
            if (Objects.equals("A", overallGrade)) {
              var name = user.getFirstName() + " " + user.getLastName();

              responseList.add(
                  ReportCardConfigDto.GradeAndPercentageResponse.builder()
                      .grade(overallGrade)
                      .percentage(String.format("%.2f", overallPercentage))
                      .studentName(name)
                      .studentId(student.getId())
                      .sectionName(student.getSection().getName())
                      .build());
            }
          } catch (Exception ignored) {
          }
        });
    return responseList;
  }

  public List<Map<String, Integer>> getGradeCounts(
      List<Student> students, String subjectSlug, boolean isBalavatika) {
    Map<String, Integer> gradeCounts = new LinkedHashMap<>();
    int t1TotalStudents = 0;
    int t2TotalStudents = 0;

    for (Student student : students) {
      var term1 = buildTable1(isBalavatika, student.getId());
      var term1Data = term1.stream().filter(x -> x.subjectSlug().equals(subjectSlug)).toList();
      var term2 = buildTable4(isBalavatika, student.getId(), term1);
      var term2Data = term2.stream().filter(x -> x.subjectSlug().equals(subjectSlug)).toList();

      if (term1Data.isEmpty()) continue;

      var term1Marks = term1Data.get(0);
      gradeCounts.merge(term1Marks.grade() + "(T1)", 1, Integer::sum);
      t1TotalStudents++;
      if (term2Data.isEmpty()) continue;
      var term2Marks = term2Data.get(0);
      gradeCounts.merge(term2Marks.grade() + "(T2)", 1, Integer::sum);
      t2TotalStudents++;
    }

    gradeCounts.put("T1TOTALSTUDENTS", t1TotalStudents);
    gradeCounts.put("T2TOTALSTUDENTS", t2TotalStudents);

    Map<String, Integer> sortedMap = new TreeMap<>(gradeCounts);
    return List.of(sortedMap);
  }
}
