package com.wexl.erp.leaves.service;

import static com.wexl.retail.util.Constants.BACK_SLASH;

import com.wexl.erp.leaves.dto.StudentLeaveRequestDto;
import com.wexl.erp.leaves.dto.StudentSearchDto;
import com.wexl.erp.leaves.model.LeaveStatus;
import com.wexl.erp.leaves.model.StudentLeaveRequest;
import com.wexl.erp.leaves.repository.StudentLeaveRequestRepository;
import com.wexl.retail.auth.AuthService;
import com.wexl.retail.auth.UserRoleHelper;
import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.commons.storage.S3FileUploadResult;
import com.wexl.retail.commons.util.DateTimeUtil;
import com.wexl.retail.communications.circulars.service.CommunicationFeature;
import com.wexl.retail.erp.attendance.domain.*;
import com.wexl.retail.erp.attendance.repository.SectionAttendanceDetailRepository;
import com.wexl.retail.erp.attendance.repository.SectionAttendanceRepository;
import com.wexl.retail.model.Teacher;
import com.wexl.retail.model.User;
import com.wexl.retail.notification.service.EventNotificationService;
import com.wexl.retail.notifications.dto.NotificationDto;
import com.wexl.retail.notifications.model.NotificationType;
import com.wexl.retail.notifications.service.NotificationsService;
import com.wexl.retail.repository.StudentRepository;
import com.wexl.retail.repository.TeacherRepository;
import com.wexl.retail.repository.UserRepository;
import com.wexl.retail.section.domain.Section;
import com.wexl.retail.section.repository.TeacherSectionRepository;
import com.wexl.retail.storage.StorageService;
import java.sql.Date;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

@Service
@Slf4j
@RequiredArgsConstructor
public class LeaveService {

  private final StudentLeaveRequestRepository studentLeaveRequestRepository;
  private final SectionAttendanceDetailRepository sectionAttendanceDetailRepository;
  private final NotificationsService notificationsService;
  private final DateTimeUtil dateTimeUtil;
  private final TeacherSectionRepository teacherSectionRepository;
  private final StudentRepository studentRepository;
  private final UserRepository userRepository;
  private final StorageService storageService;
  private final SectionAttendanceRepository sectionAttendanceRepository;
  private final EventNotificationService eventNotificationService;
  private final AuthService authService;
  private final TeacherRepository teacherRepository;

  private StudentLeaveRequest buildStudentLeaveRequest(StudentLeaveRequestDto.Request request) {
    StudentLeaveRequest leaveRequest = new StudentLeaveRequest();
    leaveRequest.setFromDate(dateTimeUtil.convertEpochToIso8601(request.fromDate()));
    leaveRequest.setToDate(dateTimeUtil.convertEpochToIso8601(request.toDate()));
    leaveRequest.setLeaveReason(request.leaveReason());
    leaveRequest.setAttachmentUrl(request.attachmentUrl());
    leaveRequest.setLink(request.link());
    leaveRequest.setStatus(LeaveStatus.PENDING);
    leaveRequest.setAppliedDate(LocalDateTime.now());
    return leaveRequest;
  }

  private StudentLeaveRequestDto.Response buildStudentLeaveResponse(
      StudentLeaveRequest leaveRequest) {
    Optional<User> user = userRepository.findByAuthUserId(leaveRequest.getReviewedBy());
    return StudentLeaveRequestDto.Response.builder()
        .leaveId(leaveRequest.getId())
        .studentId(leaveRequest.getStudent().getId())
        .studentName(
            leaveRequest.getStudent().getUserInfo().getFirstName()
                + " "
                + leaveRequest.getStudent().getUserInfo().getLastName())
        .gradeName(leaveRequest.getStudent().getSection().getGradeName())
        .gradeSlug(leaveRequest.getStudent().getSection().getGradeSlug())
        .studentSection(leaveRequest.getStudent().getSection().getName())
        .fromDate(DateTimeUtil.convertIso8601ToEpoch(leaveRequest.getFromDate()))
        .toDate(DateTimeUtil.convertIso8601ToEpoch(leaveRequest.getToDate()))
        .leaveReason(leaveRequest.getLeaveReason())
        .attachmentUrl(leaveRequest.getAttachmentUrl())
        .link(leaveRequest.getLink())
        .status(leaveRequest.getStatus())
        .reviewedBy(
            (user.isPresent() ? user.get().getFirstName() + " " + user.get().getLastName() : null))
        .reviewedOn(
            leaveRequest.getReviewedOn() != null
                ? DateTimeUtil.convertIso8601ToEpoch(leaveRequest.getReviewedOn())
                : null)
        .appliedDate(DateTimeUtil.convertIso8601ToEpoch(leaveRequest.getAppliedDate()))
        .build();
  }

  @Transactional
  public StudentLeaveRequestDto.Response applyForLeave(
      String orgSlug, String studentAuthId, StudentLeaveRequestDto.Request request) {
    User user = userRepository.getUserByAuthUserId(studentAuthId);
    var student = user.getStudentInfo();

    StudentLeaveRequest leaveRequest = buildStudentLeaveRequest(request);
    leaveRequest.setStudent(student);
    leaveRequest.setOrgSlug(orgSlug);

    StudentLeaveRequest savedRequest = studentLeaveRequestRepository.save(leaveRequest);

    var sectionTeachers = teacherSectionRepository.findAllBySection(student.getSection());
    if (sectionTeachers.isEmpty()) {
      throw new ApiException(
          InternalErrorCodes.NO_RECORD_FOUND, "No teachers found for this sectionName");
    }

    Set<Long> teacherIds =
        sectionTeachers.stream()
            .map(teacherSection -> teacherSection.getTeacher().getId())
            .collect(Collectors.toSet());

    var adminTeachers = teacherRepository.getAllAdminsByOrg(orgSlug);
    teacherIds.addAll(adminTeachers.stream().map(Teacher::getId).toList());

    LocalDateTime appliedDate = LocalDateTime.now();
    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd-MM-yyyy");

    String message =
        String.format(
            "Student %s has applied for leave from %s to %s. Applied on: %s. Reason: %s.",
            (user.getFirstName() + " " + user.getLastName()),
            dateTimeUtil.convertEpochToIso8601(request.fromDate()).format(formatter),
            dateTimeUtil.convertEpochToIso8601(request.toDate()).format(formatter),
            appliedDate.format(formatter),
            request.leaveReason());

    NotificationDto.NotificationRequest notificationRequest =
        NotificationDto.NotificationRequest.builder()
            .title("New Leave Request")
            .notificationType(com.wexl.retail.notifications.model.NotificationType.LEAVE_REQUEST)
            .feature(CommunicationFeature.LEAVE)
            .message(message)
            .attachment(
                request.attachmentUrl() != null
                    ? Collections.singletonList(request.attachmentUrl())
                    : null)
            .link(request.link() != null ? Collections.singletonList(request.link()) : null)
            .userAuthId(studentAuthId)
            .teacherIds(teacherIds.stream().toList())
            .build();

    notificationsService.createNotificationByStudent(orgSlug, notificationRequest);

    log.info("Leave request created with ID: {}", savedRequest.getId());
    return buildStudentLeaveResponse(savedRequest);
  }

  public List<StudentLeaveRequestDto.Response> getStudentLeaveRequests(
      String orgSlug, String studentAuthId) {

    var user = userRepository.getUserByAuthUserId(studentAuthId);
    var student = user.getStudentInfo();
    List<StudentLeaveRequest> leaveRequests =
        studentLeaveRequestRepository.findByOrgSlugAndStudentOrderByAppliedDateDescStatusAsc(
            orgSlug, studentRepository.findById(student.getId()).get());

    return leaveRequests.stream().map(this::buildStudentLeaveResponse).collect(Collectors.toList());
  }

  @Transactional
  public void deleteLeaveRequest(Long leaveId) {
    log.info("Deleting leave request {}", leaveId);
    studentLeaveRequestRepository.deleteById(leaveId);
    log.info("Leave request {} successfully deleted", leaveId);
  }

  @Transactional
  public void approveOrRejectLeaveRequest(
      String orgSlug,
      String teacherAuthId,
      Long leaveId,
      StudentLeaveRequestDto.ApprovalRequest approvalRequest) {

    StudentLeaveRequest leaveRequest =
        studentLeaveRequestRepository
            .findById(leaveId)
            .orElseThrow(
                () ->
                    new ApiException(
                        InternalErrorCodes.NO_RECORD_FOUND, "Leave request not found"));

    leaveRequest.setStatus(approvalRequest.status());
    leaveRequest.setReviewedBy(teacherAuthId);
    leaveRequest.setReviewedOn(LocalDateTime.now());

    StudentLeaveRequest updatedRequest = studentLeaveRequestRepository.save(leaveRequest);

    if (approvalRequest.status() == LeaveStatus.APPROVED) {
      updateAttendanceRecords(updatedRequest, updatedRequest.getStudent().getSection().getId());
    }

    NotificationDto.NotificationRequest notificationRequest =
        NotificationDto.NotificationRequest.builder()
            .title("Leave Request " + approvalRequest.status())
            .userAuthId(
                teacherAuthId != null
                    ? teacherAuthId
                    : authService.getUserDetails().getAuthUserId())
            .feature(CommunicationFeature.LEAVE)
            .message(
                String.format(
                    "Your leave request from %s  to  %s has been %s",
                    leaveRequest.getFromDate().toLocalDate(),
                    leaveRequest.getToDate().toLocalDate(),
                    approvalRequest.status().toString().toLowerCase()))
            .studentIds(List.of(leaveRequest.getStudent().getId()))
            .notificationType(
                approvalRequest.status() == LeaveStatus.APPROVED
                    ? com.wexl.retail.notifications.model.NotificationType.LEAVE_APPROVED
                    : com.wexl.retail.notifications.model.NotificationType.LEAVE_DISAPPROVED)
            .build();

    notificationsService.createNotificationByTeacher(
        orgSlug, notificationRequest, teacherAuthId, false);

    if (approvalRequest.status() == LeaveStatus.APPROVED) {
      eventNotificationService.sendPushNotificationForUser(
          leaveRequest.getStudent().getUserInfo().getAuthUserId(),
          notificationRequest.message(),
          orgSlug,
          NotificationType.LEAVE_REQUEST,
          "Leave Notification");
    }

    log.info("Leave request {} updated to status {}", leaveId, updatedRequest.getStatus());
  }

  @Transactional
  public StudentLeaveRequestDto.Response updateLeaveRequest(
      String studentAuthId, Long leaveId, StudentLeaveRequestDto.Request request) {
    log.info(
        "Updating leave request {} for student {} from {} to {}",
        leaveId,
        studentAuthId,
        request.fromDate(),
        request.toDate());

    StudentLeaveRequest existingRequest =
        studentLeaveRequestRepository
            .findByIdAndStudent(
                leaveId, userRepository.getUserByAuthUserId(studentAuthId).getStudentInfo())
            .orElseThrow(
                () ->
                    new ApiException(
                        InternalErrorCodes.NO_RECORD_FOUND, "Leave request not found"));

    if (existingRequest.getStatus() != LeaveStatus.PENDING) {
      throw new ApiException(
          InternalErrorCodes.INVALID_REQUEST,
          "Cannot update a leave request that has already been "
              + existingRequest.getStatus().toString().toLowerCase());
    }

    existingRequest.setFromDate(dateTimeUtil.convertEpochToIso8601(request.fromDate()));
    existingRequest.setToDate(dateTimeUtil.convertEpochToIso8601(request.toDate()));
    existingRequest.setLeaveReason(request.leaveReason());
    existingRequest.setAttachmentUrl(request.attachmentUrl());
    existingRequest.setLink(request.link());

    StudentLeaveRequest updatedRequest = studentLeaveRequestRepository.save(existingRequest);
    log.info("Leave request {} successfully updated", leaveId);
    return buildStudentLeaveResponse(updatedRequest);
  }

  private void updateAttendanceRecords(StudentLeaveRequest leaveRequest, Long sectionId) {
    if (!leaveRequest.getStatus().toString().equalsIgnoreCase("Approved")) {
      return;
    }
    DateTimeUtil dateTimeUtil = new DateTimeUtil();
    var fromDate = leaveRequest.getFromDate().toLocalDate();
    var toDate = leaveRequest.getToDate().toLocalDate();
    List<SectionAttendance> sectionAttendances =
        sectionAttendanceRepository.findBySectionIdAndDateIdBetween(
            sectionId,
            dateTimeUtil.convertToIntegerFormat(Date.valueOf(fromDate)),
            dateTimeUtil.convertToIntegerFormat(Date.valueOf(toDate)));
    for (SectionAttendance sectionAttendance : sectionAttendances) {
      Optional<SectionAttendanceDetails> optionalStudentAttendanceDetails =
          sectionAttendance.getAttendanceDetails().stream()
              .filter(
                  sectionAttendanceDetails ->
                      sectionAttendanceDetails.getStudent().getId()
                          == leaveRequest.getStudent().getId())
              .findFirst();
      List<SectionAttendanceDetails> sectionAttendanceDetails = new ArrayList<>();
      if (optionalStudentAttendanceDetails.isEmpty()) {
        SectionAttendanceDetails newStudentAttendanceDetails = new SectionAttendanceDetails();
        newStudentAttendanceDetails.setStudent(leaveRequest.getStudent());
        newStudentAttendanceDetails.setSectionAttendance(sectionAttendance);
        newStudentAttendanceDetails.setAttendanceStatus("leave");
        newStudentAttendanceDetails.setAfternoonAttendanceStatus("leave");
        sectionAttendanceDetails.add(newStudentAttendanceDetails);
      } else {
        SectionAttendanceDetails sectionAttendanceDetailsOfStudent =
            optionalStudentAttendanceDetails.get();
        sectionAttendanceDetailsOfStudent.setAttendanceStatus("leave");
        sectionAttendanceDetailsOfStudent.setAfternoonAttendanceStatus("leave");
        sectionAttendanceDetails.add(sectionAttendanceDetailsOfStudent);
      }
      sectionAttendanceDetailRepository.saveAll(sectionAttendanceDetails);
    }
  }

  public List<StudentLeaveRequestDto.Response> getStudentLeaveRequestsByStudentName(
      StudentSearchDto.Request request, String authUserId, String orgSlug) {
    List<User> matchingUsers =
        userRepository.findByOrgSlugAndSearchKey(orgSlug, request.searchKey() + "%");
    Optional<User> authUser = userRepository.findByAuthUserId(authUserId);

    boolean isAdmin = UserRoleHelper.get().isOrgAdmin(authUser.get());
    List<Long> teacherSectionList =
        isAdmin
            ? null
            : authUser.get().getTeacherInfo().getSections().stream().map(Section::getId).toList();

    List<Long> studentIds =
        matchingUsers.stream()
            .filter(
                user -> {
                  if (user.getStudentInfo() != null) {
                    if (!isAdmin) {
                      return teacherSectionList.contains(
                          user.getStudentInfo().getSection().getId());
                    } else return true;
                  }
                  return false;
                })
            .map(user -> user.getStudentInfo().getId())
            .collect(Collectors.toList());

    if (studentIds.isEmpty()) {
      return Collections.emptyList();
    }

    List<StudentLeaveRequest> studentsWithLeaveRequests =
        studentLeaveRequestRepository.findByStudentIdInOrderByAppliedDateDescStatusAsc(studentIds);

    return studentsWithLeaveRequests.stream()
        .map(this::buildStudentLeaveResponse)
        .collect(Collectors.toList());
  }

  public List<StudentLeaveRequestDto.Response> getAllLeaveRequestsSorted(
      String orgSlug, String authUserId, String gradeSlug, String sectionName) {
    var user = userRepository.findByAuthUserId(authUserId);
    if (UserRoleHelper.get().isOrgAdmin(user.get())) {
      return studentLeaveRequestRepository
          .findAllByOrgSlugOrderByAppliedDateDescStatusAsc(orgSlug)
          .stream()
          .map(this::buildStudentLeaveResponse)
          .filter(
              response ->
                  (Objects.isNull(gradeSlug) || Objects.isNull(sectionName))
                      || (response.gradeSlug().equalsIgnoreCase(gradeSlug)
                          && response.studentSection().equalsIgnoreCase(sectionName)))
          .collect(Collectors.toList());
    } else {
      List<Long> teacherSectionIds =
          user.get().getTeacherInfo().getSections().stream().map(Section::getId).toList();
      List<StudentLeaveRequest> pendingRequests =
          studentLeaveRequestRepository.findBySectionIds(teacherSectionIds);
      return pendingRequests.stream()
          .map(this::buildStudentLeaveResponse)
          .filter(
              response ->
                  (Objects.isNull(gradeSlug) || Objects.isNull(sectionName))
                      || (response.gradeSlug().equalsIgnoreCase(gradeSlug)
                          && response.studentSection().equalsIgnoreCase(sectionName)))
          .collect(Collectors.toList());
    }
  }

  public List<S3FileUploadResult> uploadLeaveAttachments(
      String orgSlug, String studentAuthId, List<MultipartFile> multipartFiles) {
    List<S3FileUploadResult> uploadResults = new ArrayList<>();
    for (MultipartFile multipartFile : multipartFiles) {
      String fileName = multipartFile.getOriginalFilename();
      String filePath = constructLeaveAttachmentFilePath(orgSlug, studentAuthId, fileName);
      storageService.uploadFile(multipartFile, filePath);
      uploadResults.add(
          S3FileUploadResult.builder()
              .path(filePath)
              .url(storageService.generatePreSignedUrlForFetch(filePath))
              .build());
    }
    return uploadResults;
  }

  private String constructLeaveAttachmentFilePath(
      String orgSlug, String studentAuthId, String fileName) {
    return orgSlug
        .concat(BACK_SLASH + "students")
        .concat(BACK_SLASH + studentAuthId)
        .concat(BACK_SLASH + "leaves_attachments")
        .concat(BACK_SLASH + LocalDate.now())
        .concat(BACK_SLASH + fileName);
  }
}
