package com.wexl.erp.fees.service.fineType;

import com.wexl.erp.fees.model.*;
import com.wexl.erp.fees.repository.CumulativeFineRepository;
import com.wexl.erp.fees.repository.FeePaymentDetailsRepository;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class CumulativeRule implements FineTypeRule {

  private final CumulativeFineRepository cumulativeFineRepository;
  private final FeePaymentDetailsRepository feePaymentDetailsRepository;

  @Override
  public boolean supports(FeeGroupFeeType feeGroupFeeType) {
    return FineType.CUMULATIVE.equals(feeGroupFeeType.getFineType());
  }

  @Override
  public Double calculateFine(FeeHead feeHead, FeeGroupFeeType feeGroupFeeType) {
    LocalDateTime dueDateTime = feeHead.getDueDate();
    var feePaymentDetails = feePaymentDetailsRepository.findByFeeHead(feeHead);

    LocalDateTime latestCreatedAt =
        feePaymentDetails == null || feePaymentDetails.isEmpty()
            ? null
            : feePaymentDetails.stream()
                .map(FeePaymentDetail::getCreatedAt)
                .filter(Objects::nonNull)
                .max(Comparator.naturalOrder())
                .orElse(null)
                .toLocalDateTime();

    if (latestCreatedAt != null && dueDateTime != null) {
      double paidAmount =
          (feeHead.getPaidAmount() != null ? feeHead.getPaidAmount() : 0.0)
              + feeHead.getDiscountAmount();

      if (latestCreatedAt.isBefore(dueDateTime) && paidAmount >= feeHead.getAmount()) {
        return 0.0;
      }
    }
    if (latestCreatedAt == null && LocalDate.now().isBefore(dueDateTime.toLocalDate())) {
      return 0.0;
    }

    List<CumulativeFine> cumulativeData =
        cumulativeFineRepository.findAllByFeeGroupFeeType(feeGroupFeeType);
    double totalFine = 0.0;
    LocalDate dueDate = dueDateTime.toLocalDate();
    double fineAmount =
        feeGroupFeeType.getFineAmount() == 0.0 ? 0.0 : feeGroupFeeType.getFineAmount();
    LocalDate today = latestCreatedAt != null ? latestCreatedAt.toLocalDate() : LocalDate.now();
    LocalDate calcTillDate = today.minusDays(1);

    cumulativeData.sort(Comparator.comparing(CumulativeFine::getDueDate));

    if (!cumulativeData.isEmpty()
        && cumulativeData.getFirst().getDueDate().toLocalDate().isBefore(today)) {
      long daysBeforeFirstSlab = calcTillDate.toEpochDay() - dueDate.toEpochDay();
      if (daysBeforeFirstSlab > 0) {
        totalFine += daysBeforeFirstSlab * fineAmount;
      }
    } else {
      long days = calcTillDate.toEpochDay() - dueDate.toEpochDay();
      if (days > 0) {
        totalFine += days * fineAmount;
      }
      return totalFine;
    }

    for (int i = 0; i < cumulativeData.size(); i++) {
      CumulativeFine current = cumulativeData.get(i);
      LocalDate currentDueDate =
          current.getDueDate() != null ? current.getDueDate().toLocalDate() : null;

      if (currentDueDate == null || currentDueDate.isAfter(calcTillDate)) {
        continue;
      }

      LocalDate fineStart = currentDueDate.plusDays(1);
      LocalDate fineEnd;

      if (i + 1 < cumulativeData.size()) {
        LocalDate nextDueDate = cumulativeData.get(i + 1).getDueDate().toLocalDate();
        fineEnd = nextDueDate.minusDays(1);
      } else {
        fineEnd = calcTillDate;
      }

      if (fineEnd.isAfter(calcTillDate)) {
        fineEnd = calcTillDate;
      }

      if (!fineStart.isAfter(fineEnd)) {
        long days = fineEnd.toEpochDay() - fineStart.toEpochDay() + 1;
        totalFine += days * current.getFineAmount();
      }
    }

    return totalFine;
  }
}
