package com.wexl.erp.paymentGateway.types;

import com.wexl.erp.fees.dto.FeeDto;
import com.wexl.erp.fees.model.FeeHead;
import com.wexl.erp.paymentGateway.dto.PaymentGatewayDto;
import com.wexl.erp.paymentGateway.dto.PaymentMethod;
import com.wexl.erp.paymentGateway.model.PaymentGatewayDetail;

public interface PaymentGatewayRule {
  boolean supports(PaymentMethod paymentMethod);

  PaymentGatewayDto.Response initiatePayment(
      String orgSlug,
      FeeDto.CollectFeeRequest request,
      FeeHead feeHead,
      PaymentGatewayDetail config);

  void verifyPayment(
      String orgSlug,
      String paymentId,
      PaymentGatewayDto.VerifyPaymentRequest verifyPaymentRequest,
      PaymentGatewayDetail config);
}
