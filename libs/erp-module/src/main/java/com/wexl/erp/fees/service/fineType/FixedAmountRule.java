package com.wexl.erp.fees.service.fineType;

import com.wexl.erp.fees.model.FeeGroupFeeType;
import com.wexl.erp.fees.model.FeeHead;
import com.wexl.erp.fees.model.FineType;
import com.wexl.erp.fees.repository.FeeGroupFeeTypeRepository;
import java.time.LocalDateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class FixedAmountRule implements FineTypeRule {

  private final FeeGroupFeeTypeRepository feeGroupFeeTypeRepository;

  @Autowired
  public FixedAmountRule(FeeGroupFeeTypeRepository feeGroupFeeTypeRepository) {
    this.feeGroupFeeTypeRepository = feeGroupFeeTypeRepository;
  }

  @Override
  public boolean supports(FeeGroupFeeType feeHeadFeeType) {
    return FineType.AMOUNT.equals(feeHeadFeeType.getFineType());
  }

  @Override
  public Double calculateFine(FeeHead feeHead, FeeGroupFeeType feeGroupFeeType) {
    LocalDateTime dueDate = feeHead.getDueDate();
    Double fineAmount = feeHead.getFineAmount() != null ? feeHead.getFineAmount() : 0.0;

    if (dueDate != null && dueDate.isBefore(LocalDateTime.now())) {
      return fineAmount;
    }
    return 0.0;
  }
}
