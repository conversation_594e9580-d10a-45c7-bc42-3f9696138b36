package com.wexl.erp.paymentGateway.types;

import com.wexl.erp.fees.dto.FeeDto;
import com.wexl.erp.fees.model.FeeHead;
import com.wexl.erp.paymentGateway.dto.PaymentGatewayDto;
import com.wexl.erp.paymentGateway.model.PaymentGatewayDetail;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class PaymentGatewayEngine {
  private final List<PaymentGatewayRule> paymentGatewayRules;

  public PaymentGatewayDto.Response initiatePayment(
      String orgSlug,
      FeeDto.CollectFeeRequest request,
      FeeHead feeHead,
      PaymentGatewayDetail config) {
    return paymentGatewayRules.stream()
        .filter(rule -> rule.supports(request.paymentMethod()))
        .findFirst()
        .map(rule -> rule.initiatePayment(orgSlug, request, feeHead, config))
        .orElseThrow(
            () ->
                new IllegalArgumentException(
                    "No payment rule found for method: " + request.paymentMethod()));
  }

  public void verifyPayment(
      String orgSlug,
      String paymentId,
      PaymentGatewayDto.VerifyPaymentRequest verifyPaymentRequest,
      PaymentGatewayDetail config) {

    paymentGatewayRules.stream()
        .filter(rule -> rule.supports(verifyPaymentRequest.paymentMethod()))
        .findFirst()
        .ifPresentOrElse(
            rule -> rule.verifyPayment(orgSlug, paymentId, verifyPaymentRequest, config),
            () -> {
              throw new IllegalArgumentException(
                  "No payment rule found for method: " + verifyPaymentRequest.paymentMethod());
            });
  }
}
