package com.wexl.erp.paymentGateway.repository;

import com.wexl.erp.paymentGateway.dto.PaymentMethod;
import com.wexl.erp.paymentGateway.model.PaymentGatewayDetail;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface PaymentGatewayDetailRepository extends JpaRepository<PaymentGatewayDetail, Long> {
  PaymentGatewayDetail findByOrgSlugAndPaymentMethod(String orgSlug, PaymentMethod paymentMethod);

  List<PaymentGatewayDetail> findByOrgSlug(String orgSlug);
}
