package com.wexl.erp.paymentGateway.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import org.json.JSONObject;

public record PaymentGatewayDto() {
  @Builder
  public record Response(String referenceId, JSONObject jsonObject, String txnId, String hash) {}

  @Builder
  public record InitiatePaymentResponse(
      @JsonProperty("payment_id") String paymentId,
      @JsonProperty("payment_status") PaymentStatus paymentStatus,
      @JsonProperty("rzPay_order_id") String rzPayOrderId,
      @JsonProperty("rzPay_key") String rzPayKey,
      @JsonProperty("hash") String easeBuzzHash,
      String message) {}

  @Builder
  public record PaymentResponse(
      @JsonProperty("payment_id") String paymentId,
      @JsonProperty("payment_status") PaymentStatus status,
      String message) {}

  @Builder
  public record VerifyPaymentRequest(
      @JsonProperty("payment_method") PaymentMethod paymentMethod,
      @JsonProperty("payment_id") String razorpayPaymentId,
      @JsonProperty("order_id") String razorpayOrderId,
      @JsonProperty("signature") String razorpaySignature) {}

  @Builder
  public record CallBackRequest(
      String txnId,
      String status,
      String amount,
      String productinfo,
      String firstName,
      String email,
      String hash) {}

  @Builder
  public record PaymentMethodResponse(PaymentMethod paymentMethod, String orgSlug) {}
}
