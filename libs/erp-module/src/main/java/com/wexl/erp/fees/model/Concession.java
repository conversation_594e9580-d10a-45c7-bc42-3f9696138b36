package com.wexl.erp.fees.model;

import com.wexl.retail.model.Model;
import com.wexl.retail.model.User;
import jakarta.persistence.*;
import java.time.LocalDateTime;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Entity
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "concessions")
public class Concession extends Model {
  @Id @GeneratedValue private UUID id;

  private ConcessionType type;

  private Double value;

  private String description;

  @Column(name = "published_at")
  private LocalDateTime publishedAt;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "createdBy")
  private User createdBy;

  @Column(name = "org_slug", nullable = false)
  private String orgSlug;
}
