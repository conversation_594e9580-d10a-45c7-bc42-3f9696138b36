package com.wexl.retail.guardian.repository;

import com.wexl.retail.guardian.model.Guardian;
import com.wexl.retail.guardian.model.GuardianRole;
import com.wexl.retail.model.Student;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface GuardianRepository extends JpaRepository<Guardian, Long> {
  List<Guardian> findByRelationTypeAndStudent(GuardianRole relationType, Student student);

  Guardian findByStudentAndIsPrimary(Student studentId, Boolean status);

  List<Guardian> findByStudentId(Long studentId);
}
