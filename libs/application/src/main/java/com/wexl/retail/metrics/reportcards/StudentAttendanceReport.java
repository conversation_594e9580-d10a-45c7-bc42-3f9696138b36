package com.wexl.retail.metrics.reportcards;

import com.wexl.retail.metrics.dto.GenericMetricRequest;
import com.wexl.retail.metrics.dto.GenericMetricResponse;
import com.wexl.retail.metrics.handler.AbstractMetricHandler;
import com.wexl.retail.metrics.handler.MetricHandler;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import org.springframework.stereotype.Service;

@Service
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class StudentAttendanceReport extends AbstractMetricHandler implements MetricHandler {

  @Override
  public String name() {
    return "student-attendance-report";
  }

  @Override
  public List<GenericMetricResponse> executeInternal(
      String org, GenericMetricRequest genericMetricRequest) {
    List<Long> board =
        Optional.ofNullable(genericMetricRequest.getInput().get(BOARD))
            .map(List.class::cast)
            .orElse(Collections.emptyList());
    List<String> gradeList =
        Optional.ofNullable(genericMetricRequest.getInput().get(GRADE))
            .map(List.class::cast)
            .orElse(Collections.emptyList());
    List<Long> section =
        Optional.ofNullable(genericMetricRequest.getInput().get(SECTIONS))
            .map(List.class::cast)
            .orElse(Collections.emptyList());
    List<String> fromDate = (List<String>) genericMetricRequest.getInput().get(FROM_DATE);
    List<String> toDate = (List<String>) genericMetricRequest.getInput().get(TO_DATE);
    String sessionType =
        Optional.ofNullable(genericMetricRequest.getInput().get(SESSION_TYPE))
            .map(Object::toString)
            .orElse(null);
    return erpAttendanceService.getStudentAttendanceReport(
        org, board, gradeList, section, fromDate.getFirst(), toDate.getFirst(), sessionType);
  }
}
