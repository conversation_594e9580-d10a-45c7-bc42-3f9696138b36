package com.wexl.retail.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.wexl.retail.globalprofile.model.RoleTemplate;
import com.wexl.retail.guardian.model.Guardian;
import com.wexl.retail.qrcode.domain.QrCode;
import com.wexl.retail.section.domain.Section;
import com.wexl.retail.student.registration.dto.StudentAttributeData;
import io.hypersistence.utils.hibernate.type.json.JsonType;
import jakarta.persistence.*;
import java.util.List;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.Type;

@Getter
@Setter
@Entity
@Table(name = "students")
public class Student extends Model {

  @Id
  @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "students-sequence-generator")
  @SequenceGenerator(
      name = "students-sequence-generator",
      sequenceName = "students_seq",
      allocationSize = 1)
  private long id;

  private String schoolName;
  private int boardId;
  private int classId;
  private String rollNumber;
  private String academicYearSlug;
  private String classRollNumber;

  private Character active = '1';

  @OneToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "userId")
  private User userInfo;

  private int modifiedBy;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "section_id")
  private Section section;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "qr_code_id")
  private QrCode qrCode;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "class_representative")
  private Student crStudentAuthUserId;

  @Column(name = "prev_student_id")
  private Long prevStudentId;

  @Column(name = "ext_ref")
  private Long extRef;

  @JsonIgnore
  @Type(JsonType.class)
  @Column(columnDefinition = "jsonb")
  private StudentAttributeData attributes;

  @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
  @JoinColumn(name = "student_id")
  private List<Guardian> guardians;

  @OneToOne(cascade = CascadeType.ALL)
  private RoleTemplate roleTemplate;

  private Boolean feeDefaulter = false;

  @Column(name = "fee_due_amount")
  private Long feeDueAmount;
}
