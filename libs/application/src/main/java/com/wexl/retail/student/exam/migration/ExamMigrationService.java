package com.wexl.retail.student.exam.migration;

import static org.apache.commons.lang3.StringUtils.trim;

import com.wexl.retail.ai.AiQuestionAnalysis;
import com.wexl.retail.ai.dto.ExamAnalysis.AiQuestionAnalysisResponseList;
import com.wexl.retail.ai.dto.ExamAnalysis.PromptAnswerContent;
import com.wexl.retail.ai.dto.ExamAnalysis.PromptQuestionContent;
import com.wexl.retail.auth.AuthService;
import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.commons.util.DateTimeUtil;
import com.wexl.retail.content.ContentService;
import com.wexl.retail.content.model.QuestionType;
import com.wexl.retail.elp.service.SpeechEvaluationService;
import com.wexl.retail.model.Student;
import com.wexl.retail.offlinetest.dto.ReportCardDto;
import com.wexl.retail.offlinetest.model.ReportCardTemplate;
import com.wexl.retail.offlinetest.model.ReportCardTemplateType;
import com.wexl.retail.offlinetest.repository.ReportCardTemplateRepository;
import com.wexl.retail.offlinetest.service.OfflineTestReportService;
import com.wexl.retail.repository.StudentRepository;
import com.wexl.retail.speech.SpeechService;
import com.wexl.retail.speech.dto.SpeechEvaluation;
import com.wexl.retail.student.answer.ExamAnswer;
import com.wexl.retail.student.exam.Exam;
import com.wexl.retail.student.exam.ExamActivityService;
import com.wexl.retail.student.exam.ExamRepository;
import com.wexl.retail.student.exam.SectionWiseData;
import com.wexl.retail.student.exam.SectionWiseData.SectionWiseTestData;
import com.wexl.retail.student.exam.publisher.ExamCompletionEventPublisher;
import com.wexl.retail.task.repository.TaskRepository;
import com.wexl.retail.test.competitive.TestDefinitionValidatorProcessor;
import com.wexl.retail.test.schedule.domain.ScheduleTest;
import com.wexl.retail.test.schedule.domain.ScheduleTestStudent;
import com.wexl.retail.test.schedule.domain.TestScheduleStudentAnswer;
import com.wexl.retail.test.schedule.dto.StudentTestAttemptStatus;
import com.wexl.retail.test.schedule.dto.TestStudentStatus;
import com.wexl.retail.test.schedule.repository.ScheduleTestRepository;
import com.wexl.retail.test.schedule.repository.ScheduleTestStudentRepository;
import com.wexl.retail.test.schedule.repository.StudentScheduleTestAnswerRepository;
import com.wexl.retail.test.school.domain.TestCategory;
import com.wexl.retail.test.school.domain.TestDefinition;
import com.wexl.retail.test.school.domain.TestDefinitionSection;
import com.wexl.retail.test.school.domain.TestQuestion;
import com.wexl.retail.test.school.dto.PbqDto;
import com.wexl.retail.test.school.dto.QuestionDto;
import com.wexl.retail.test.school.repository.TestDefinitionSectionRepository;
import com.wexl.retail.test.school.repository.TestQuestionRepository;
import com.wexl.retail.test.school.service.TestDefinitionService;
import com.wexl.retail.util.Constants;
import com.wexl.retail.util.ValidationUtils;
import jakarta.transaction.Transactional;
import java.sql.Timestamp;
import java.time.Instant;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

@Slf4j
@Service
@RequiredArgsConstructor
public class ExamMigrationService {

  private final StudentRepository studentRepository;
  private final ScheduleTestStudentRepository scheduleTestStudentRepository;
  private final StudentScheduleTestAnswerRepository studentScheduleTestAnswerRepository;
  private final ExamRepository examRepository;
  private final AuthService authService;
  private final TestQuestionRepository testQuestionRepository;
  private final ExamActivityService examActivityService;
  private final TestDefinitionService testDefinitionService;
  private final ScheduleTestRepository scheduleTestRepository;
  private final DateTimeUtil dateTimeUtil;
  public static final String NEGATIVE_MARKS_SCORED = "negative_marks_scored";
  public static final String MARKS_SCORED = "marksScored";
  public static final String IMPROMPTU_SPEECH = "Impromptu Speech";
  public final TestDefinitionValidatorProcessor testDefinitionValidatorProcessor;
  public final TestDefinitionSectionRepository testDefinitionSectionRepository;
  public final ExamCompletionEventPublisher examCompletionEventPublisher;
  public final List<AiQuestionAnalysis> aiQuestionAnalysis;
  private final ContentService contentService;
  private final List<SpeechService> speechService;
  private final SpeechEvaluationService speechEvaluationService;
  private final ValidationUtils validationUtils;
  private final TaskRepository taskRepository;
  private final OfflineTestReportService offlineTestReportService;
  private final ReportCardTemplateRepository reportCardTemplateRepository;

  @Value("${app.contentToken}")
  String contentBearerToken;

  @Transactional
  public List<ScheduleTestStudent> identifyValidTestScheduleStudentEntries() {
    var scheduleTestStudentList =
        scheduleTestStudentRepository.findByStatusAndResultProcessingTimeBefore(
            String.valueOf(TestStudentStatus.SUBMITTED), LocalDateTime.now());
    if (scheduleTestStudentList.isEmpty()) {
      return new ArrayList<>();
    }
    return scheduleTestStudentList;
  }

  public void migrateSubmittedExams(List<ScheduleTestStudent> scheduleTestStudentList) {
    log.debug("Migration count = {}", (long) scheduleTestStudentList.size());

    scheduleTestStudentList.forEach(
        scheduleTestStudent -> {
          log.debug(
              "Migration started for test_schedule_student_id:{}", scheduleTestStudent.getId());
          processMigration(
              scheduleTestStudent,
              getContentQuestionByDefinitionAndType(
                  scheduleTestStudent.getScheduleTest().getTestDefinition(),
                  QuestionType.SUBJECTIVE));
          log.debug(
              "Migration completed for test_schedule_student_id:{}", scheduleTestStudent.getId());
        });
  }

  public List<QuestionDto.Question> getContentQuestionByDefinitionAndType(
      TestDefinition testDefinition, QuestionType questionType) {
    List<TestQuestion> testQuestions =
        testDefinition.getTestDefinitionSections().stream()
            .map(TestDefinitionSection::getTestQuestions)
            .flatMap(List::stream)
            .filter(q -> questionType.name().equalsIgnoreCase(q.getType()))
            .toList();
    return getQuestions(testQuestions, testDefinition.getOrganization());
  }

  private List<QuestionDto.Question> getQuestions(
      List<TestQuestion> testQuestions, String organization) {
    if (testQuestions.isEmpty()) {
      return new ArrayList<>();
    }

    List<QuestionDto.SearchQuestionResponse> questionResponses =
        testDefinitionService.getQuestionsByUuid(contentBearerToken, testQuestions, organization);
    return questionResponses.stream()
        .map(QuestionDto.SearchQuestionResponse::questions)
        .flatMap(List::stream)
        .toList();
  }

  public Exam processMigration(
      ScheduleTestStudent scheduleTestStudent, List<QuestionDto.Question> questionsFromContent) {
    var studentAnswers =
        studentScheduleTestAnswerRepository.findAllByTssUuidAndUserName(
            scheduleTestStudent.getUuid(), scheduleTestStudent.getStudent().getAuthUserId());
    try {
      var testCategory = scheduleTestStudent.getScheduleTest().getTestDefinition().getCategory();
      if (testCategory.equals(TestCategory.DEFAULT)
          || testCategory.equals(TestCategory.BET)
          || testCategory.equals(TestCategory.LWS)
          || testCategory.equals(TestCategory.BET_LWS)) {
        return saveExam(scheduleTestStudent, studentAnswers, questionsFromContent);
      }
      var filterSectionIds =
          studentAnswers.stream().map(TestScheduleStudentAnswer::getSectionId).distinct().toList();
      var sections = testDefinitionSectionRepository.findAllById(filterSectionIds);
      var tssa =
          testDefinitionValidatorProcessor.processOptionalQuestions(
              scheduleTestStudent, studentAnswers, sections);
      return saveExam(scheduleTestStudent, tssa, questionsFromContent);
    } catch (Exception ex) {
      scheduleTestStudent.setStatus(String.valueOf(TestStudentStatus.ERROR_IN_PROCESSING));
      scheduleTestStudent.setFailureReason(
          trimToLength(ex.getMessage() + ExceptionUtils.getStackTrace(ex)));
      scheduleTestStudentRepository.save(scheduleTestStudent);
      log.error(ex.getMessage(), ex);
    }
    return null;
  }

  private String trimToLength(String message) {
    return message.substring(0, Math.min(message.length(), 1000));
  }

  private Exam saveExam(
      ScheduleTestStudent scheduleTestStudent,
      List<TestScheduleStudentAnswer> tssa,
      List<QuestionDto.Question> questions) {
    final Exam examEntity = buildExamEntity(scheduleTestStudent, tssa);
    updateExamEntityWithAiAnalysis(scheduleTestStudent, examEntity, questions);
    examEntity.setMarksScored(calculateMarkScored(examEntity.getExamAnswers()));
    var exam = examRepository.save(examEntity);
    saveSpchExamAnswerMarksScored(exam, tssa);
    scheduleTestStudent.setStatus(String.valueOf(TestStudentStatus.COMPLETED));
    scheduleTestStudentRepository.save(scheduleTestStudent);
    examCompletionEventPublisher.publishExamCompletion(exam);
    examActivityService.notifyTestCompletion(exam, testDefinitionService);
    if (isBetTest(scheduleTestStudent.getScheduleTest().getTestDefinition())) {
      ReportCardTemplate reportCardTemplate =
          getBetReportCardTemplateByGrade(examEntity.getStudent().getSection().getGradeSlug());
      var request = buildRequest(exam, reportCardTemplate);
      offlineTestReportService.constructReportCardModelBodyByAsync(
          reportCardTemplate, exam.getTestDefinition().getOrganization(), request);
    }
    return exam;
  }

  private ReportCardTemplate getBetReportCardTemplateByGrade(String gradeSlug) {
    String templateConfig =
        Map.of("stdg", "inter-bet-report-card.xml").getOrDefault(gradeSlug, "bet-report-card.xml");

    return reportCardTemplateRepository
        .findByReportCardTemplateTypeAndConfig(ReportCardTemplateType.CUSTOM, templateConfig)
        .orElseThrow(
            () ->
                new ApiException(
                    InternalErrorCodes.INVALID_REQUEST, "Report card template not found"));
  }

  private ReportCardDto.Request buildRequest(Exam exam, ReportCardTemplate reportCardTemplate) {
    return ReportCardDto.Request.builder()
        .offlineTestDefinitionId(exam.getScheduleTest().getId())
        .studentAuthId(exam.getStudent().getUserInfo().getAuthUserId())
        .templateId(reportCardTemplate.getId())
        .build();
  }

  private boolean isBetTest(TestDefinition testDefinition) {
    return Objects.nonNull(testDefinition.getCategory())
        && testDefinition.getCategory().equals(TestCategory.BET);
  }

  public void saveSpchExamAnswerMarksScored(Exam exam, List<TestScheduleStudentAnswer> tssa) {
    try {
      if (tssa == null || tssa.isEmpty()) {
        return;
      }

      QuestionDto.QuestionResponse questionResponse =
          testDefinitionService.getQuestionResponsePreconfigured(exam.getTestDefinition(), 0);

      var questionMap =
          questionResponse.testDefinitionSectionResponses().stream()
              .map(QuestionDto.TestDefinitionSectionResponse::questions)
              .flatMap(Collection::stream)
              .collect(Collectors.groupingBy(QuestionDto.Question::uuid));

      for (TestScheduleStudentAnswer studentAnswer : tssa) {
        String questionUuid = studentAnswer.getQuestionUuid();

        if (!QuestionType.SPCH
                .toString()
                .equalsIgnoreCase(studentAnswer.getQuestionType().toString())
            || studentAnswer.getSpchSelectedAnswer() == null) {
          continue;
        }
        ExamAnswer examAnswer =
            exam.getExamAnswers().stream()
                .filter(ans -> ans.getQuestionUuid().equals(questionUuid))
                .findFirst()
                .orElse(null);

        if (examAnswer == null) {
          continue;
        }
        var speechQuestions = questionMap.get(questionUuid);
        if (speechQuestions == null || speechQuestions.isEmpty()) {
          continue;
        }
        var speechQuestion = speechQuestions.getFirst();

        SpeechEvaluation.SpeechResponse speechResponse =
            speechService
                .getFirst()
                .pronunciationAssessment(
                    speechEvaluationService.massage(speechQuestion.question()),
                    studentAnswer.getSpchSelectedAnswer(),
                    String.valueOf(examAnswer.getId()),
                    IMPROMPTU_SPEECH.equalsIgnoreCase(speechQuestion.category()));

        examAnswer.setMarksScoredPerQuestion(calculateSpeechAnsweredMarks(speechResponse));
      }

      Float totalScore =
          exam.getExamAnswers().stream()
              .map(ExamAnswer::getMarksScoredPerQuestion)
              .filter(Objects::nonNull)
              .reduce(0f, Float::sum);
      exam.setMarksScored(totalScore);
      examRepository.save(exam);
    } catch (Exception e) {
      log.info("Error while saving speech exam marks: {}", e.getMessage(), e);
    }
  }

  private Float calculateSpeechAnsweredMarks(SpeechEvaluation.SpeechResponse speechResponse) {
    var ieltsScore = speechResponse.assessment().ieltsScore().floatValue();
    var normalizedScore = (ieltsScore / 9f) * 1f;
    var marksScored = validationUtils.formatMarks(normalizedScore);
    return (float) marksScored;
  }

  private void updateExamEntityWithAiAnalysis(
      ScheduleTestStudent scheduleTestStudent,
      Exam examEntity,
      List<QuestionDto.Question> questions) {
    final TestCategory category =
        scheduleTestStudent.getScheduleTest().getTestDefinition().getCategory();
    var examAnswers =
        examEntity.getExamAnswers().stream()
            .filter(
                ea ->
                    ea.getType().equalsIgnoreCase(QuestionType.SUBJECTIVE.toString())
                        && Objects.nonNull(ea.getSubjectiveWrittenAnswer()))
            .toList();
    if ((TestCategory.BET.equals(category) && !CollectionUtils.isEmpty(examAnswers)
        || TestCategory.BET_LWS.equals(category))) {
      // update Exam object with AI Results
      final AiQuestionAnalysisResponseList aiQuestionAnalysisResponseList =
          aiQuestionAnalysis
              .getFirst()
              .analyzeQuestions(
                  constructPromptQuestionContents(examAnswers, questions),
                  constructPromptAnswerContents(examAnswers));
      updateExamEntityWithAiResults(examEntity, aiQuestionAnalysisResponseList);
    }
  }

  private void updateExamEntityWithAiResults(
      Exam examEntity, AiQuestionAnalysisResponseList aiQuestionAnalysisResponseList) {
    aiQuestionAnalysisResponseList
        .response()
        .forEach(
            analysis -> {
              var examAnswer =
                  examEntity.getExamAnswers().stream()
                      .filter(ea -> analysis.question().equals(ea.getQuestionId()))
                      .toList();
              if (examAnswer.isEmpty()) {
                return;
              }
              examAnswer.getFirst().setAiAnalysis(analysis.analysis());
              Float aiMarks = Float.valueOf(String.valueOf(analysis.marks()));
              examAnswer.getFirst().setAiMarks(aiMarks);
              examAnswer.getFirst().setMarksScoredPerQuestion(aiMarks);
            });
  }

  private List<PromptAnswerContent> constructPromptAnswerContents(List<ExamAnswer> examAnswers) {
    List<PromptAnswerContent> promptAnswerContents = new ArrayList<>();

    examAnswers.forEach(
        ea ->
            promptAnswerContents.add(
                PromptAnswerContent.builder()
                    .questionNumber(ea.getQuestionId())
                    .answer(ea.getSubjectiveWrittenAnswer())
                    .build()));

    return promptAnswerContents;
  }

  private List<PromptQuestionContent> constructPromptQuestionContents(
      List<ExamAnswer> examAnswers, List<QuestionDto.Question> questions) {
    List<PromptQuestionContent> promptQuestionContents = new ArrayList<>();
    var questionMap = questions.stream().collect(Collectors.groupingBy(QuestionDto.Question::uuid));

    examAnswers.forEach(
        ea ->
            promptQuestionContents.add(
                PromptQuestionContent.builder()
                    .questionNumber(ea.getQuestionId())
                    .text(questionMap.get(ea.getQuestionUuid()).getFirst().question())
                    .marks(ea.getMarksPerQuestion())
                    .answer(questionMap.get(ea.getQuestionUuid()).getFirst().explanation())
                    .build()));

    return promptQuestionContents;
  }

  private Exam buildExamEntity(
      ScheduleTestStudent scheduleTestStudent, List<TestScheduleStudentAnswer> studentAnswers) {
    var testDefinition = scheduleTestStudent.getScheduleTest().getTestDefinition();
    final Student student =
        studentRepository.findByUserId(scheduleTestStudent.getStudent().getId());
    Exam exam = new Exam();
    exam.setEndTime(
        Objects.nonNull(scheduleTestStudent.getEndTime())
            ? Timestamp.valueOf(scheduleTestStudent.getEndTime())
            : Timestamp.from(Instant.now()));
    exam.setStartTime(
        Objects.nonNull(scheduleTestStudent.getStartTime())
            ? Timestamp.valueOf(scheduleTestStudent.getStartTime())
            : Timestamp.from(Instant.now()));
    exam.setStudent(student);
    exam.setCompleted(true);
    exam.setNoOfQuestions(testDefinition.getNoOfQuestions());
    exam.setExamType(Constants.MOCK_TEST);
    exam.setAllowedDuration(
        dateTimeUtil.getDuration(
            scheduleTestStudent.getStartTime(), scheduleTestStudent.getEndTime()));
    exam.setTestDefinition(testDefinition);
    exam.setScheduleTest(scheduleTestStudent.getScheduleTest());
    var examAnswers = buildExamAnswersEntity(studentAnswers, exam);
    exam.setExamAnswers(examAnswers);
    exam.setTotalMarks(
        (float) examAnswers.stream().mapToDouble(ExamAnswer::getMarksPerQuestion).sum());
    exam.setRef(String.valueOf(scheduleTestStudent.getUuid()));
    exam.setExamAttributes(
        buildSectionData(testDefinition.getTestDefinitionSections(), examAnswers));
    exam.setAttemptedQuestionsCount(getAttemptedQuestionCount(scheduleTestStudent).size());
    return exam;
  }

  public List<TestScheduleStudentAnswer> getAttemptedQuestionCount(
      ScheduleTestStudent scheduleTestStudent) {

    List<TestScheduleStudentAnswer> testScheduleStudentAnswers =
        studentScheduleTestAnswerRepository.findAllByTssUuid(scheduleTestStudent.getUuid());
    return testScheduleStudentAnswers.stream()
        .filter(
            tssa -> tssa.getAttemptStatus().name().equals(StudentTestAttemptStatus.ANSWERED.name()))
        .toList();
  }

  private SectionWiseData.ExamAttributes buildSectionData(
      List<TestDefinitionSection> testDefinitionSections, List<ExamAnswer> examAnswers) {
    List<SectionWiseTestData> sectionWiseTestData = new ArrayList<>();
    testDefinitionSections.forEach(
        testDefinitionSection ->
            sectionWiseTestData.add(
                SectionWiseTestData.builder()
                    .sectionId(testDefinitionSection.getId())
                    .sectionName(testDefinitionSection.getName())
                    .noOfQuestions(testDefinitionSection.getNoOfQuestions())
                    .totalMarks(calculateTotalMark(testDefinitionSection))
                    .marksScored(buildMarksScored(testDefinitionSection, examAnswers))
                    .build()));
    return buildExamAttributes(sectionWiseTestData);
  }

  private SectionWiseData.ExamAttributes buildExamAttributes(
      List<SectionWiseTestData> sectionWiseTestData) {

    return SectionWiseData.ExamAttributes.builder()
        .sectionWiseTestData(sectionWiseTestData)
        .build();
  }

  private Float calculateTotalMark(TestDefinitionSection testDefinitionSectionId) {
    List<TestQuestion> testQuestion =
        testQuestionRepository.findByTestDefinitionSectionOrderById(testDefinitionSectionId);
    return (float) testQuestion.stream().mapToDouble(TestQuestion::getMarks).sum();
  }

  private Float buildMarksScored(
      TestDefinitionSection testDefinitionSection, List<ExamAnswer> examAnswers) {
    List<TestQuestion> testQuestions = testDefinitionSection.getTestQuestions();
    List<String> stringList = testQuestions.stream().map(TestQuestion::getQuestionUuid).toList();
    var examQuestionAnswers =
        examAnswers.stream()
            .filter(examAnswer -> stringList.contains(examAnswer.getQuestionUuid()))
            .toList();
    return (float)
        examQuestionAnswers.stream().mapToDouble(ExamAnswer::getMarksScoredPerQuestion).sum();
  }

  private Float calculateMarkScored(List<ExamAnswer> examMarks) {
    var marks = (float) examMarks.stream().mapToDouble(ExamAnswer::getMarksScoredPerQuestion).sum();
    return Float.parseFloat(Constants.DECIMAL_FORMAT.format(marks));
  }

  private List<ExamAnswer> buildExamAnswersEntity(
      List<TestScheduleStudentAnswer> studentAnswers, Exam exam) {
    List<ExamAnswer> examAnswersList = new ArrayList<>();
    Set<String> questionUuids = new HashSet<>();

    studentAnswers.forEach(
        answer -> {
          // Process the question only once
          if (questionUuids.contains(answer.getQuestionUuid())) {
            return;
          }

          var testQuestion =
              testQuestionRepository.findByIdAndQuestionUuid(
                  answer.getTestQuestionId(), answer.getQuestionUuid());
          boolean isSubjective =
              answer
                  .getQuestionType()
                  .toString()
                  .equalsIgnoreCase(QuestionType.SUBJECTIVE.toString());

          ExamAnswer examAnswer = new ExamAnswer();
          var results = calculateMarks(testQuestion, answer);
          examAnswer.setQuestionUuid(answer.getQuestionUuid());
          examAnswer.setQuestionId(answer.getTestQuestionId());
          examAnswer.setAttempted(answer.getAttemptStatus().toString().contains("ANSWERED"));
          examAnswer.setType(answer.getQuestionType().getType());
          examAnswer.setNegativeMarks(results.get(NEGATIVE_MARKS_SCORED));
          examAnswer.setExam(exam);
          examAnswer.setExamReference(exam.getId());
          examAnswer.setIsMobile(authService.isUserLoginByMobile());
          examAnswer.setSelectedOption(answer.getMcqSelectedAnswer());
          examAnswer.setMsqSelectedAnswer(answer.getMsqSelectedAnswer());
          examAnswer.setNatSelectedAnswer(answer.getNatSelectedAnswer());
          examAnswer.setSubjectiveWrittenAnswer(answer.getSubjectiveWrittenAnswer());
          examAnswer.setFbqSelectedAnswer(answer.getFbqSelectedAnswer());
          examAnswer.setYesNoSelectedAnswer(answer.getYesNoSelectedAnswer());
          examAnswer.setAmcqSelectedAnswer(answer.getAmcqSelectedAnswer());
          examAnswer.setSpchSelectedAnswer(answer.getSpchSelectedAnswer());
          examAnswer.setActive(true);
          examAnswer.setCorrect(!isSubjective && results.get(MARKS_SCORED) > 0);
          examAnswer.setMarksScoredPerQuestion(isSubjective ? 0.0f : results.get(MARKS_SCORED));
          examAnswer.setMarksPerQuestion(testQuestion.getMarks());
          examAnswer.setSubtopicSlug(testQuestion.getSubtopicSlug());
          examAnswer.setPbqAnswers(getPbqResponse(testQuestion, answer));
          examAnswer.setDdfbqAttemptedAnswer(
              processDdfbqAnswers(testQuestion, answer.getDdfbqAttemptedAnswer()));
          examAnswersList.add(examAnswer);
          questionUuids.add(answer.getQuestionUuid());
        });
    return examAnswersList;
  }

  public void mapDdFbqMarkScored(
      TestQuestion testQuestion, TestScheduleStudentAnswer studentAnswers, Map<String, Float> map) {
    if (Objects.isNull(studentAnswers.getDdfbqAttemptedAnswer())
        || Objects.isNull(testQuestion.getDdFbqAnswer())) {
      map.put(MARKS_SCORED, 0F);
      map.put(NEGATIVE_MARKS_SCORED, 0F);
      return;
    }
    var correctWords = testQuestion.getDdFbqAnswer().words();
    List<String> attemptedWords = extractWordsInDollars(studentAnswers.getDdfbqAttemptedAnswer());

    int wrongCount = 0;
    for (int i = 0; i < correctWords.size(); i++) {
      if (i >= attemptedWords.size() || !correctWords.get(i).equals(attemptedWords.get(i))) {
        wrongCount++;
      }
    }
    var correctedWords = correctWords.size() - wrongCount;
    var marksPerWord = (float) testQuestion.getMarks() / correctWords.size();
    var marksScored = correctedWords * marksPerWord;
    map.put(MARKS_SCORED, marksScored);
    map.put(NEGATIVE_MARKS_SCORED, 0.0f);
  }

  public static List<String> extractWordsInDollars(String text) {
    List<String> words = new ArrayList<>();
    Pattern pattern = Pattern.compile("\\$(.*?)\\$");
    Matcher matcher = pattern.matcher(text);

    while (matcher.find()) {
      words.add(matcher.group(1));
    }
    return words;
  }

  public static String processDdfbqAnswers(TestQuestion testQuestion, String attemptedAnswer) {
    if (!QuestionType.DDFBQ.name().equals(testQuestion.getType())) {
      return null;
    }
    try {
      List<String> correctWords = testQuestion.getDdFbqAnswer().words();
      if (StringUtils.isEmpty(attemptedAnswer)) {
        return null;
      }
      StringBuilder result = new StringBuilder();
      Matcher matcher = Pattern.compile("\\$(.*?)\\$").matcher(attemptedAnswer);
      int index = 0;
      while (matcher.find()) {
        String attemptedWord = matcher.group(1);
        String replacement = "<span style='color:red'>" + "_____" + "</span>";
        if (!Objects.equals("null", attemptedWord)) {
          String correctWord = index < correctWords.size() ? correctWords.get(index) : "";
          replacement =
              attemptedWord.equals(correctWord)
                  ? "<span style='color:green'>" + attemptedWord + "</span>"
                  : "<span style='color:red'>" + attemptedWord + "</span>";
        }
        matcher.appendReplacement(result, Matcher.quoteReplacement(replacement));
        index++;
      }
      matcher.appendTail(result);
      return "<p>" + result + "</p>";
    } catch (Exception e) {
      log.error("failed to process the drag and drop answer: {}", e.getMessage(), e);
      return null;
    }
  }

  private Map<String, Float> calculateMarks(
      TestQuestion testQuestion, TestScheduleStudentAnswer studentAnswers) {
    Map<String, Float> map = new HashMap<>();
    if (QuestionType.MCQ.equals(studentAnswers.getQuestionType())) {
      mapMcqMarkScored(testQuestion, studentAnswers, map);
    } else if (QuestionType.MSQ.equals(studentAnswers.getQuestionType())) {
      mapMsqMarkScored(testQuestion, studentAnswers, map);
    } else if (QuestionType.YESNO.equals(studentAnswers.getQuestionType())) {
      mapYesNoMarkScored(testQuestion, studentAnswers, map);
    } else if (QuestionType.NAT.equals(studentAnswers.getQuestionType())) {
      mapNatMarkScored(testQuestion, studentAnswers, map);
    } else if (QuestionType.FBQ.equals(studentAnswers.getQuestionType())) {
      mapFbqMarkScored(testQuestion, studentAnswers, map);
    } else if (QuestionType.PBQ.equals(studentAnswers.getQuestionType())) {
      mapPbqMarkScored(testQuestion, studentAnswers, map);
    } else if (QuestionType.AMCQ.equals(studentAnswers.getQuestionType())) {
      mapAmcqMarkScored(testQuestion, studentAnswers, map);
    } else if (QuestionType.SPCH.equals(studentAnswers.getQuestionType())) {
      mapSpchMarkScored(testQuestion, studentAnswers, map);
    } else if (QuestionType.DDFBQ.equals(studentAnswers.getQuestionType())) {
      mapDdFbqMarkScored(testQuestion, studentAnswers, map);
    }

    return map;
  }

  private void mapSpchMarkScored(
      TestQuestion testQuestion, TestScheduleStudentAnswer studentAnswers, Map<String, Float> map) {
    if (Objects.isNull(studentAnswers.getSpchSelectedAnswer())) {
      map.put(MARKS_SCORED, 0F);
      map.put(NEGATIVE_MARKS_SCORED, 0F);
    } else if (Objects.nonNull(testQuestion.getSpchAnswer())) {
      map.put(MARKS_SCORED, Float.valueOf(testQuestion.getMarks()));
      map.put(NEGATIVE_MARKS_SCORED, 0F);
    } else {
      var negativeMarks =
          testQuestion.getNegativeMarks() == 0
              ? testQuestion.getNegativeMarks()
              : -testQuestion.getNegativeMarks();
      map.put(MARKS_SCORED, Float.valueOf(testQuestion.getMarks()));
      map.put(NEGATIVE_MARKS_SCORED, negativeMarks);
    }
  }

  private void mapAmcqMarkScored(
      TestQuestion testQuestion, TestScheduleStudentAnswer studentAnswers, Map<String, Float> map) {
    if (Objects.isNull(studentAnswers.getAmcqSelectedAnswer())) {
      map.put(MARKS_SCORED, 0F);
      map.put(NEGATIVE_MARKS_SCORED, 0F);
    } else if (Objects.equals(
        studentAnswers.getAmcqSelectedAnswer(), testQuestion.getAmcqAnswer())) {
      map.put(MARKS_SCORED, Float.valueOf(testQuestion.getMarks()));
      map.put(NEGATIVE_MARKS_SCORED, 0F);
    } else {
      var negativeMarks =
          testQuestion.getNegativeMarks() == 0
              ? testQuestion.getNegativeMarks()
              : -testQuestion.getNegativeMarks();
      map.put(MARKS_SCORED, negativeMarks);
      map.put(NEGATIVE_MARKS_SCORED, negativeMarks);
    }
  }

  private PbqDto.Data getPbqResponse(
      TestQuestion testQuestion, TestScheduleStudentAnswer studentAnswers) {
    var pbqAnswers = studentAnswers.getPbqAnswers();
    if (!QuestionType.PBQ.equals(studentAnswers.getQuestionType()) || Objects.isNull(pbqAnswers)) {
      return null;
    }
    List<PbqDto.Answers> pbqAnswerResponses = new ArrayList<>();

    testQuestion
        .getPbqAnswers()
        .answers()
        .forEach(
            testAnswer ->
                pbqAnswers
                    .answers()
                    .forEach(
                        selectedAnswer ->
                            pbqAnswerResponses.add(
                                PbqDto.Answers.builder()
                                    .mcq(
                                        PbqDto.Mcq.builder()
                                            .questionUuid(selectedAnswer.mcq().questionUuid())
                                            .answer(
                                                selectedAnswer
                                                        .mcq()
                                                        .questionUuid()
                                                        .equals(testAnswer.mcq().questionUuid())
                                                    ? testAnswer.mcq().answer()
                                                    : null)
                                            .selectedAnswer(selectedAnswer.mcq().selectedAnswer())
                                            .build())
                                    .build())));
    return PbqDto.Data.builder().answers(pbqAnswerResponses).build();
  }

  private void mapPbqMarkScored(
      TestQuestion testQuestion, TestScheduleStudentAnswer studentAnswers, Map<String, Float> map) {
    if (studentAnswers.getPbqAnswers() != null) {
      var pbqAnswers = studentAnswers.getPbqAnswers().answers();
      List<Float> markObtained = new ArrayList<>();
      var marks = (float) testQuestion.getMarks() / pbqAnswers.size();
      var totalNegativeMarks = testQuestion.getNegativeMarks();
      var negativeMarkForEachQuestion =
          totalNegativeMarks == 0 ? 0 : totalNegativeMarks / pbqAnswers.size();
      AtomicInteger unAttemptedQuestionsCount = new AtomicInteger();
      AtomicInteger attemptedAndCorrect = new AtomicInteger();
      AtomicInteger attemptedAndWrong = new AtomicInteger();

      pbqAnswers.forEach(
          pbqAnswer -> {
            if (pbqAnswer.type() != QuestionType.MCQ) {
              throw new ApiException(
                  InternalErrorCodes.INVALID_REQUEST, "error.UnsupportedQuestionType");
            }

            studentAnswers.setMcqSelectedAnswer(pbqAnswer.mcq().selectedAnswer());
            testQuestion.setMcqAnswer(
                getMcqAnswer(testQuestion.getPbqAnswers().answers(), pbqAnswer.mcq()));
            float obtainedMarks =
                pbqAnswer.mcq().selectedAnswer() == null
                    ? 0
                    : getMcqMarkScored(pbqAnswer.mcq().selectedAnswer(), testQuestion, marks);
            markObtained.add(obtainedMarks);

            if (pbqAnswer.mcq().selectedAnswer() == null) {
              unAttemptedQuestionsCount.incrementAndGet();
            } else if (obtainedMarks == 0) {
              attemptedAndWrong.incrementAndGet();
            } else {
              attemptedAndCorrect.incrementAndGet();
            }
          });

      var marksSum = (float) markObtained.stream().mapToDouble(Float::doubleValue).sum();
      var negativeMarksScored =
          testQuestion.getNegativeMarks() == 0
              ? 0
              : attemptedAndWrong.get() * negativeMarkForEachQuestion;

      map.put(NEGATIVE_MARKS_SCORED, negativeMarksScored);
      map.put(
          MARKS_SCORED,
          negativeMarksScored == 0
              ? marksSum
              : (float) (Math.round((marksSum - negativeMarksScored) * 100.0) / 100.0));
    } else {
      map.put(MARKS_SCORED, 0F);
      map.put(NEGATIVE_MARKS_SCORED, 0F);
    }
  }

  private Float getMcqMarkScored(Integer selectedAnswer, TestQuestion testQuestion, Float marks) {
    return Objects.equals((long) selectedAnswer, testQuestion.getMcqAnswer()) ? marks : 0f;
  }

  private Long getMcqAnswer(List<PbqDto.Answers> testAnswers, PbqDto.Mcq mcq) {
    var mcqanswer =
        testAnswers.stream()
            .map(PbqDto.Answers::mcq)
            .filter(mcq1 -> mcq1.questionUuid().equals(mcq.questionUuid()))
            .findFirst()
            .orElseThrow();
    return (long) mcqanswer.answer();
  }

  private void mapNatMarkScored(
      TestQuestion testQuestion, TestScheduleStudentAnswer studentAnswers, Map<String, Float> map) {
    if (Objects.isNull(studentAnswers.getNatSelectedAnswer())) {
      map.put(MARKS_SCORED, 0F);
      map.put(NEGATIVE_MARKS_SCORED, 0F);
    } else if (Objects.equals(studentAnswers.getNatSelectedAnswer(), testQuestion.getNatAnswer())) {
      map.put(MARKS_SCORED, Float.valueOf(testQuestion.getMarks()));
      map.put(NEGATIVE_MARKS_SCORED, 0F);
    } else {
      var negativeMarks =
          testQuestion.getNegativeMarks() == 0
              ? testQuestion.getNegativeMarks()
              : -testQuestion.getNegativeMarks();
      map.put(MARKS_SCORED, negativeMarks);
      map.put(NEGATIVE_MARKS_SCORED, negativeMarks);
    }
  }

  private void mapFbqMarkScored(
      TestQuestion testQuestion, TestScheduleStudentAnswer studentAnswers, Map<String, Float> map) {

    final String fbqSelectedAnswer = trim(studentAnswers.getFbqSelectedAnswer());
    if (Objects.isNull(fbqSelectedAnswer)) {
      map.put(MARKS_SCORED, 0F);
      map.put(NEGATIVE_MARKS_SCORED, 0F);
    } else {
      final String fbqAnswer = trim(testQuestion.getFbqAnswer());
      if (Objects.equals(fbqSelectedAnswer, fbqAnswer)) {
        map.put(MARKS_SCORED, Float.valueOf(testQuestion.getMarks()));
        map.put(NEGATIVE_MARKS_SCORED, 0F);
      } else {
        var negativeMarks =
            testQuestion.getNegativeMarks() == 0
                ? testQuestion.getNegativeMarks()
                : -testQuestion.getNegativeMarks();
        map.put(MARKS_SCORED, negativeMarks);
        map.put(NEGATIVE_MARKS_SCORED, negativeMarks);
      }
    }
  }

  private void mapYesNoMarkScored(
      TestQuestion testQuestion, TestScheduleStudentAnswer studentAnswers, Map<String, Float> map) {
    if (Objects.isNull(studentAnswers.getYesNoSelectedAnswer())) {
      map.put(MARKS_SCORED, 0F);
      map.put(NEGATIVE_MARKS_SCORED, 0F);
    } else if (Objects.equals(studentAnswers.getYesNoSelectedAnswer(), testQuestion.getYesNo())) {
      map.put(MARKS_SCORED, Float.valueOf(testQuestion.getMarks()));
      map.put(NEGATIVE_MARKS_SCORED, 0F);
    } else {
      var negativeMarks =
          testQuestion.getNegativeMarks() == 0
              ? testQuestion.getNegativeMarks()
              : -testQuestion.getNegativeMarks();
      map.put(MARKS_SCORED, negativeMarks);
      map.put(NEGATIVE_MARKS_SCORED, negativeMarks);
    }
  }

  private void mapMsqMarkScored(
      TestQuestion testQuestion, TestScheduleStudentAnswer studentAnswers, Map<String, Float> map) {
    if (Objects.isNull(studentAnswers.getMsqSelectedAnswer())) {
      map.put(MARKS_SCORED, 0F);
      map.put(NEGATIVE_MARKS_SCORED, 0F);
    } else if (new HashSet<>(testQuestion.getMsqAnswer())
            .containsAll(studentAnswers.getMsqSelectedAnswer())
        && testQuestion.getMsqAnswer().size() == studentAnswers.getMsqSelectedAnswer().size()) {
      map.put(MARKS_SCORED, Float.valueOf(testQuestion.getMarks()));
      map.put(NEGATIVE_MARKS_SCORED, 0F);
    } else {
      var negativeMarks =
          testQuestion.getNegativeMarks() == 0
              ? testQuestion.getNegativeMarks()
              : -testQuestion.getNegativeMarks();
      map.put(MARKS_SCORED, negativeMarks);
      map.put(NEGATIVE_MARKS_SCORED, negativeMarks);
    }
  }

  private void mapMcqMarkScored(
      TestQuestion testQuestion, TestScheduleStudentAnswer studentAnswers, Map<String, Float> map) {
    if (Objects.isNull(studentAnswers.getMcqSelectedAnswer())) {
      map.put(MARKS_SCORED, 0F);
      map.put(NEGATIVE_MARKS_SCORED, 0F);
    } else if (Objects.equals(
        (long) studentAnswers.getMcqSelectedAnswer(), testQuestion.getMcqAnswer())) {
      map.put(MARKS_SCORED, Float.valueOf(testQuestion.getMarks()));
      map.put(NEGATIVE_MARKS_SCORED, 0F);
    } else {
      var negativeMarks =
          testQuestion.getNegativeMarks() == 0
              ? testQuestion.getNegativeMarks()
              : -testQuestion.getNegativeMarks();
      map.put(MARKS_SCORED, negativeMarks);
      map.put(NEGATIVE_MARKS_SCORED, negativeMarks);
    }
  }

  @Transactional
  public void autoSubmitExam() {
    var scheduleTestStudentList = scheduleTestStudentRepository.findTestsToBeAutoSubmitted();
    if (!scheduleTestStudentList.isEmpty()) {
      scheduleTestStudentList.forEach(
          x -> {
            x.setStatus(String.valueOf(TestStudentStatus.SUBMITTED));
            x.setEndTime(LocalDateTime.now());
          });
      scheduleTestStudentRepository.saveAll(scheduleTestStudentList);
    }
  }

  public void migrateExamAttributes(Long testScheduleId) {
    final Optional<ScheduleTest> possibleScheduleTest =
        scheduleTestRepository.findById(testScheduleId);
    if (possibleScheduleTest.isEmpty()) {
      return;
    }

    final List<Exam> allExams =
        examRepository.findAllByScheduleTestIn(List.of(possibleScheduleTest.get()));

    allExams.forEach(this::migrateExamAttributes);
    examRepository.saveAll(allExams);
  }

  private void migrateExamAttributes(Exam exam) {
    final List<ExamAnswer> cleanedUpExamAnswers =
        exam.getExamAnswers().stream()
            .collect(
                Collectors.toMap(
                    ExamAnswer::getQuestionUuid,
                    Function.identity(),
                    (existing, replacement) -> existing))
            .values()
            .stream()
            .toList();

    exam.setExamAttributes(
        buildSectionData(
            exam.getTestDefinition().getTestDefinitionSections(), cleanedUpExamAnswers));
  }
}
