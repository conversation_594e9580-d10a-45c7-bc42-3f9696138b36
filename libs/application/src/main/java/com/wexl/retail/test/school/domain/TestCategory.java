package com.wexl.retail.test.school.domain;

import com.wexl.retail.test.school.processor.*;
import lombok.Getter;

@Getter
public enum TestCategory {
  EAMCET("EAMCET", new EamcetTestCategoryProcessor()),
  NEET("NEET", new NEETTestCategoryProcessor()),
  LEGACYIIT("IIT24", new LegacyIITTestCategoryProcessor()),
  DEFAULT("DEFAULT", new DefaultTestCategoryProcessor()),
  ELP("ELP", new ELPTestCategoryProcessor()),
  STANDARD("STANDARD", new DefaultTestCategoryProcessor()),
  CUSTOM("CUSTOM", new CustomTestCategoryProcessor()),
  BET("BET", new BetTestCategoryProcessor()),
  LWS("LWS", new LwsTestCategoryProcessor()),
  IIT("IIT", new IITTestCategoryProcessor()),
  BET_LWS("BET-LWS", new BetLwsTestCategoryProcessor()),
  STANDARDV2("STANDARDV2", new DefaultTestCategoryProcessor()),
  SPEAK_LAB("SL", new SpeakingLabTestCategoryProcessor()),
  WRITE_LAB("WL", new WritingLabTestCategoryProcessor()),
  PLACEMENT_LAB("PL", new PlacementTestCategoryProcessor());

  private String value;
  private TestCategoryProcessor processor;

  TestCategory(String value, TestCategoryProcessor processor) {
    this.processor = processor;
    this.value = value;
  }

  @Override
  public String toString() {
    return this.value;
  }

  public static TestCategory fromValue(String value) {
    if (value == null || value.isEmpty()) {
      throw new IllegalArgumentException("Value cannot be null or empty!");
    }

    for (TestCategory enumEntry : TestCategory.values()) {
      if (enumEntry.toString().equals(value)) {
        return enumEntry;
      }
    }

    throw new IllegalArgumentException("Cannot create enum from " + value + " value!");
  }
}
