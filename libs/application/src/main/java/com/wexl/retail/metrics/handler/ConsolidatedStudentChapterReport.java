package com.wexl.retail.metrics.handler;

import com.wexl.retail.auth.AuthService;
import com.wexl.retail.auth.AuthUtil;
import com.wexl.retail.metrics.StudentChapterPerformanceService;
import com.wexl.retail.metrics.dto.GenericMetricRequest;
import com.wexl.retail.metrics.dto.GenericMetricResponse;
import com.wexl.retail.model.User;
import java.util.*;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class ConsolidatedStudentChapterReport extends AbstractMetricHandler {

  private final StudentChapterPerformanceService studentChapterPerformanceService;
  private final AuthService authService;

  @Override
  public String name() {
    return "consolidated-student-chapter-report";
  }

  @Override
  protected List<GenericMetricResponse> executeInternal(
      String org, GenericMetricRequest genericMetricRequest) {
    Long testScheduleId =
        Long.valueOf(genericMetricRequest.getInput().get(TEST_SCHEDULE_ID).toString());

    User currentUser = authService.getUserDetails();

    boolean isStudentAccess = AuthUtil.isStudent(currentUser);
    boolean isAdminOrTeacher = AuthUtil.isOrgAdmin(currentUser) || AuthUtil.isTeacher(currentUser);

    String studentAuthId = null;
    if (isStudentAccess) {

      studentAuthId = currentUser.getAuthUserId();
    }

    return studentChapterPerformanceService.getStudentChapterPerformanceReport(
        testScheduleId, isStudentAccess, studentAuthId);
  }
}
