package com.wexl.retail.auth.controller;

import com.wexl.retail.auth.AuthRequest;
import com.wexl.retail.auth.AuthResponse;
import com.wexl.retail.auth.AuthService;
import com.wexl.retail.auth.UserRoleHelper;
import com.wexl.retail.auth.dto.*;
import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.model.LoginDevice;
import com.wexl.retail.model.LoginMethod;
import com.wexl.retail.model.User;
import com.wexl.retail.model.UserRole;
import com.wexl.retail.otp.OtpResponse;
import com.wexl.retail.otp.OtpService;
import com.wexl.retail.repository.UserRepository;
import com.wexl.retail.util.MobileAppUtil;
import jakarta.validation.Valid;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RestController
@RequestMapping("/auth")
@RequiredArgsConstructor
public class AuthController {

  private final AuthService authService;
  private final UserRoleHelper userRoleHelper;
  private final OtpService otpService;
  private final UserRepository userRepository;

  @Value("${app.orgCheck.slug}")
  private String stmOrgSlug;

  @Value("${app.orgCheck.password}")
  private String stmOrgPassword;

  @PostMapping("/login")
  public LoginResponse login(
      @Valid @RequestBody LoginRequest loginRequest,
      @RequestHeader(HttpHeaders.USER_AGENT) String userAgent) {
    if (ObjectUtils.isEmpty(loginRequest)
        || StringUtils.isEmpty(loginRequest.getUsername())
        || StringUtils.isEmpty(loginRequest.getPassword())
        || StringUtils.isEmpty(loginRequest.getAppContext())) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.WrongCredentials");
    }
    var user = authService.getUserByUserName(loginRequest.getUsername());
    if (user == null) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.WrongCredentials");
    }
    if (authService.isUserBelongingToDeletedOrg(user)) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.DeletedOrg");
    }
    authService.validateDeviceDetails(user, loginRequest.getAppContext());
    var requestComingFromMobileApp = MobileAppUtil.requestComingFromMobileApp(userAgent);
    return authService.signin(
        requestComingFromMobileApp, loginRequest, LoginMethod.USERNAME_PASSWORD);
  }

  @PostMapping("/partner")
  public LoginResponse partnerLogin(@RequestBody LoginRequest loginRequest) {
    if (Objects.equals(loginRequest.getPassword(), stmOrgPassword)) {
      var user = userRepository.getUserByAuthUserId("st" + loginRequest.getUsername());
      if (user != null && !Objects.equals(user.getOrganization(), stmOrgSlug)) {
        throw new ApiException(
            InternalErrorCodes.UN_AUTHORIZED,
            "error.AuthUserID.Exist",
            new String[] {user.getUserName()});
      }
      return authService.generatePartnerToken(user);
    }
    throw new ApiException(InternalErrorCodes.UN_AUTHORIZED, "error.WrongCredentials");
  }

  @PostMapping("/org-admin")
  public ResponseEntity<Object> orgAdminAuth(
      @Valid @RequestBody AuthRequest authRequest,
      @RequestHeader(HttpHeaders.USER_AGENT) String userAgent) {

    var user = authService.retrieveUserFromToken(authRequest.getIdToken());
    validateRole(user, UserRole.ROLE_ORG_ADMIN);
    authService.validateDeviceDetails(user, authRequest.getGuid());
    authService.updateUserLastLoginAndGuid(user, authRequest.getGuid());

    var requestComingFromMobileApp = MobileAppUtil.requestComingFromMobileApp(userAgent);

    String jwtToken =
        authService.generateAccessToken(
            requestComingFromMobileApp, user, LoginMethod.USERNAME_PASSWORD);
    return ResponseEntity.status(HttpStatus.OK)
        .body(AuthResponse.builder().accessToken(jwtToken).build());
  }

  private void validateRole(User user, UserRole... userRole) {
    if (user == null) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.User.Null");
    }
    final List<UserRole> currentRoles = userRoleHelper.getUserRolesFromUser(user);
    Optional<UserRole> possibleUserRole =
        Arrays.stream(userRole).filter(currentRoles::contains).findFirst();
    if (possibleUserRole.isEmpty()) {
      log.debug(
          "A User ["
              + user.getUserName()
              + "]' whose Role is "
              + Arrays.toString(currentRoles.toArray())
              + " Trying to authenticate as "
              + Arrays.toString(userRole));
      throw new ApiException(InternalErrorCodes.UN_AUTHORIZED, "error.AccessDenied");
    }
  }

  @PostMapping("/student")
  public ResponseEntity<Object> studentAuth(
      @Valid @RequestBody AuthRequest authRequest,
      @RequestHeader(HttpHeaders.USER_AGENT) String userAgent) {

    var user = authService.retrieveUserFromToken(authRequest.getIdToken());
    validateRole(user, UserRole.ROLE_STUDENT, UserRole.ROLE_ISTUDENT);
    authService.validateVerificationStatus(user);
    authService.validateDeviceDetails(user, authRequest.getGuid());
    authService.updateUserLastLoginAndGuid(user, authRequest.getGuid());
    var requestComingFromMobileApp = MobileAppUtil.requestComingFromMobileApp(userAgent);
    String jwtToken =
        authService.generateStudentAccessToken(
            requestComingFromMobileApp, user, LoginMethod.USERNAME_PASSWORD);
    return ResponseEntity.status(HttpStatus.OK)
        .body(AuthResponse.builder().accessToken(jwtToken).build());
  }

  @PostMapping("/teacher")
  public ResponseEntity<Object> teacherAuth(
      @Valid @RequestBody AuthRequest authRequest,
      @RequestHeader(HttpHeaders.USER_AGENT) String userAgent) {
    var user = authService.retrieveUserFromToken(authRequest.getIdToken());
    validateRole(user, UserRole.ROLE_TEACHER, UserRole.ROLE_ITEACHER);
    authService.validateEmailVerified(user);
    authService.validateDeviceDetails(user, authRequest.getGuid());
    authService.updateUserLastLoginAndGuid(user, authRequest.getGuid());

    var requestComingFromMobileApp = MobileAppUtil.requestComingFromMobileApp(userAgent);
    String jwtToken =
        authService.generateTeacherAccessToken(
            requestComingFromMobileApp, user, LoginMethod.USERNAME_PASSWORD);
    return ResponseEntity.status(HttpStatus.OK)
        .body(AuthResponse.builder().accessToken(jwtToken).build());
  }

  @PostMapping
  public ResponseEntity<Object> authenticate(
      @Valid @RequestBody AuthRequest authRequest,
      @RequestHeader(HttpHeaders.USER_AGENT) String userAgent) {

    var user = authService.retrieveUserFromToken(authRequest.getIdToken());
    authService.validateDetails(user);
    authService.validateDeviceDetails(user, authRequest.getGuid());
    authService.updateUserLastLoginAndGuid(user, authRequest.getGuid());
    authService.saveLoginHistory(user, LoginMethod.USERNAME_PASSWORD, LoginDevice.MOBILE);
    var requestComingFromMobileApp = MobileAppUtil.requestComingFromMobileApp(userAgent);
    String jwtToken =
        authService.generateAccessToken(
            requestComingFromMobileApp, user, LoginMethod.USERNAME_PASSWORD);
    return ResponseEntity.status(HttpStatus.OK)
        .body(
            AuthResponse.builder()
                .accessToken(jwtToken)
                .roles(userRoleHelper.getUserRolesFromUser(user))
                .build());
  }

  @PostMapping("/mobile-number/login")
  public OtpResponse sendOtp(
      @RequestBody @Valid MobileNumberLoginDto.MobileNumberLoginOtpRequest request) {
    String verifiedMobileNumber;

    verifiedMobileNumber = authService.validateMobileNumber(request);

    return otpService.sendOtpByMsg91(verifiedMobileNumber);
  }

  @PostMapping("/mobile-number/login:verify")
  public MobileNumberLoginDto.MobileNumberLoginResponse loginWithMobileNumber(
      @RequestBody @Valid MobileNumberLoginDto.MobileNumberLoginRequest mobileNumberLoginRequest,
      @RequestHeader(HttpHeaders.USER_AGENT) String userAgent) {
    if (StringUtils.isEmpty(mobileNumberLoginRequest.appContext())) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.appContext.Null");
    }
    return otpService.verifyOtpByMsg91(mobileNumberLoginRequest, userAgent);
  }

  @PostMapping("/mobile-number/link")
  public ResponseEntity<MobileNumberLoginDto.MobileNumberLoginResponse> linkUserToMobileNumber(
      @RequestBody MobileNumberLoginDto.UpdateMobileNumberRequest request,
      @RequestHeader(HttpHeaders.USER_AGENT) String userAgent) {
    return ResponseEntity.ok(authService.updateUserMobileNumber(request, userAgent));
  }

  @PostMapping("/ping")
  public ResponseEntity<MobileNumberLoginDto.PingResponse> ping(
      @RequestBody MobileNumberLoginDto.PingRequest pingRequest,
      @RequestHeader(HttpHeaders.USER_AGENT) String userAgent) {
    var response = authService.verifyUserTokenByPing(pingRequest, userAgent);
    return ResponseEntity.status(HttpStatus.OK).body(response);
  }
}
