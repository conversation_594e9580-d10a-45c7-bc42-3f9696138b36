package com.wexl.retail.guardian.model;

import com.wexl.retail.mobile.model.CountryCode;
import com.wexl.retail.model.Model;
import com.wexl.retail.model.Student;
import jakarta.persistence.*;
import lombok.*;

@Data
@Entity
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Table(name = "guardians", schema = "public")
public class Guardian extends Model {
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;

  @Column(name = "first_name")
  private String firstName;

  @Column(name = "last_name")
  private String lastName;

  @Column(name = "relation_type")
  private GuardianRole relationType;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "student_id")
  private Student student;

  private String email;

  @Column(name = "mobile_number")
  private String mobileNumber;

  @Column(name = "is_primary")
  private Boolean isPrimary;

  @OneToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "country_code_id")
  private CountryCode countryCode;

  private String occupation;

  @Column(name = "image_url")
  private String imageUrl;
}
