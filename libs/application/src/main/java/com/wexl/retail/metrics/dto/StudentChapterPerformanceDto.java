package com.wexl.retail.metrics.dto;

import java.util.List;

public class StudentChapterPerformanceDto {

  public record StudentData(
      String studentName,
      Long studentId,
      Long userId,
      String grade,
      float totalMarksScored,
      float totalPossibleMarks,
      double overallPercentage,
      List<ChapterPerformance> chapterPerformances) {}

  public record ChapterPerformance(
      String chapterSlug,
      String chapterName,
      String questionType,
      String complexity,
      List<String> questionTags,
      int questionsCount,
      double average,
      double percentage,
      List<QuestionResponse> questionResponses) {}

  public record QuestionResponse(
      String questionUuid, String question, Float marksScored, Integer totalMarks) {}

  public record QuestionMetadata(
      String chapterSlug,
      String chapterName,
      String questionType,
      String complexity,
      List<String> questionTags,
      Integer marks,
      String questionText) {}
}
