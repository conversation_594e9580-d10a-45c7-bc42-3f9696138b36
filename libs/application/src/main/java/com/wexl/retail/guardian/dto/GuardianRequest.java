package com.wexl.retail.guardian.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.wexl.retail.guardian.model.GuardianRole;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@Builder
@NoArgsConstructor
public class GuardianRequest {

  @JsonProperty("first_name")
  private String firstName;

  @JsonProperty("last_name")
  private String lastName;

  @JsonProperty("email_id")
  private String emailId;

  @JsonProperty("mobile_number")
  private String mobileNumber;

  @JsonProperty("relation_type")
  private GuardianRole relationType;

  @NotNull
  @JsonProperty("is_primary")
  private Boolean isPrimary;

  @JsonProperty("country_code_id")
  private Long countryCodeId;

  private String occupation;

  @JsonProperty("image_url")
  private String imageUrl;
}
