package com.wexl.retail.erp.attendance.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;

public record StudentAttendanceReportDto() {

  @Builder
  public record Response(
      @JsonProperty("name") String name,
      @JsonProperty("admission_number") String admissionNumber,
      @JsonProperty("auth_id") String authId,
      @JsonProperty("user_name") String userName,
      @JsonProperty("section_name") String SectionName,
      @JsonProperty("roll_number") String RollNumber,
      @JsonProperty("grade_name") String GradeName,
      @JsonProperty("total_working_days") Long TotalWorkingDays,
      @JsonProperty("absent_days") Long AbsentDays,
      @JsonProperty("Present_days") Long PresentDays,
      @JsonProperty("half_days") Long HalfDays,
      @JsonProperty("leave_days") Long LeaveDays,
      @JsonProperty("late_comer_days") Long LateComerDays,
      @JsonProperty("ptm_days") Long PtmDays,
      @JsonProperty("class_roll_number") Long classRollNumber) {}
}
