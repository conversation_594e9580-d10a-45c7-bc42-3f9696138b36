package com.wexl.retail.test.schedule.repository;

import com.wexl.retail.model.User;
import com.wexl.retail.student.exam.school.AllScheduledTests;
import com.wexl.retail.test.schedule.domain.ScheduleTest;
import com.wexl.retail.test.schedule.domain.ScheduleTestStudent;
import com.wexl.retail.test.schedule.dto.ManagerScheduleOrgReport;
import com.wexl.retail.test.schedule.dto.ScheduleTestStudentResponse;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface ScheduleTestStudentRepository extends JpaRepository<ScheduleTestStudent, Long> {

  List<ScheduleTestStudent> findByScheduleTest(ScheduleTest scheduleTest);

  @Query(
      value =
          """
                      select ts.id as scheduleTestId, td.id as testDefinitionId, td.test_name
                             as testName, ts.start_date as startDate, ts.end_date as endDate,td.type as testType,
                             td.subject_slug as subjectSlug, tss.status as testState, tss.uuid as scheduleTestUuid
                          from test_schedule_student tss
                          left join test_schedule ts on ts.id = tss.schedule_test_id
                          left join test_definitions td on ts.test_definition_id = td.id
                          where ts.start_date between now() - INTERVAL '90 DAY' and now() + INTERVAL '90 DAY'
                            and ts.status = 'active' and td.type  in (:types)
                            and tss.student_id = :studentId  and tss.status IN ('PENDING','STARTED') and ts.end_date > now()
                          order by start_date desc""",
      nativeQuery = true)
  List<AllScheduledTests> findAllTestsForStudent(long studentId, List<String> types);

  @Query(
      value =
          """
                      select ts.id as scheduleTestId, td.id as testDefinitionId,
                      td.test_name as testName, ts.start_date as startDate,
                      ts.end_date as endDate, td.subject_slug as subjectSlug,
                      tss.status as testState
                      from test_schedule_student tss
                      left join test_schedule ts on ts.id = tss.schedule_test_id
                      left join test_definitions td on ts.test_definition_id = td.id
                      where ts.status = 'active' and td.type = 'ASSIGNMENT'
                      and tss.student_id = :studentId and tss.status in ('PENDING','STARTED') and ts.end_date > now()
                      order by start_date
                      """,
      nativeQuery = true)
  List<AllScheduledTests> getScheduledAssignments(long studentId);

  @Query(
      value =
          """
          select * from test_schedule_student tss \
          where tss.student_id = :studentId and \
          tss.schedule_test_id=:scheduleTestId and tss.status='STARTED' \
          """,
      nativeQuery = true)
  Optional<ScheduleTestStudent> findByTestScheduleIdStudentId(long studentId, long scheduleTestId);

  @Query(
      value =
          """
                  SELECT tss.schedule_test_id AS scheduleTestId,u.id AS userId,u.user_name AS userName,u.first_name AS firstName,
                  u.last_name AS lastName,s.id AS studentId,s.roll_number as rollNumber,s.class_roll_number as classRollNumber,(SELECT tss1.status FROM test_schedule_student tss1
                  WHERE tss1.schedule_test_id = tss.schedule_test_id AND tss1.student_id=tss.student_id)as studentTestStatus,
                  CASE WHEN (SELECT tss1.status FROM test_schedule_student tss1 WHERE tss1.schedule_test_id = tss.schedule_test_id
                  AND tss1.student_id=tss.student_id ORDER BY id DESC LIMIT 1) = 'COMPLETED' THEN TRUE ELSE FALSE END AS testTaken,
                  _section.id AS sectionId,_section.name AS sectionName,e.id AS examId,e.corrected AS examEvaluated,s.school_name AS instituteName
                  FROM (SELECT schedule_test_id, student_id FROM test_schedule_student GROUP BY schedule_test_id, student_id) tss
                  INNER JOIN users u ON u.id = tss.student_id INNER JOIN students s ON s.user_id = u.id
                  LEFT OUTER JOIN exams e ON e.schedule_test_id = tss.schedule_test_id AND e.student_id = s.id AND e.end_time is not null
                  INNER JOIN sections _section ON _section.id = s.section_id LEFT JOIN teacher_sections ts ON ts.section_id = _section.id
                  AND ts.teacher_id = (SELECT id FROM teacher_details td WHERE td.user_id=:teacherUserId) WHERE tss.schedule_test_id IN  (:scheduleTestIds)

                      """,
      nativeQuery = true)
  List<ScheduleTestStudentResponse> getAllStudentsByScheduledIdsAndTeacherId(
      List<Long> scheduleTestIds, long teacherUserId);

  @Query(
      value =
          """
          select * from test_schedule_student tss \
          where tss.student_id = :studentId and \
          tss.schedule_test_id=:scheduleTestId and tss.status='COMPLETED' \
          """,
      nativeQuery = true)
  Optional<ScheduleTestStudent> completedScheduledTest(long studentId, long scheduleTestId);

  @Query(
      """
      select sts from ScheduleTestStudent sts left \
      join fetch sts.scheduleTest st where st.id in (:scheduleTestIds)\
      """)
  List<ScheduleTestStudent> getAllStudentsByScheduledId(List<Long> scheduleTestIds);

  @Query(
      "select count(e) from Exam e where e.endTime is not null and e.scheduleTest=:scheduleTest and e.corrected=false")
  long getExamsNotCorrectedCount(ScheduleTest scheduleTest);

  Optional<ScheduleTestStudent> findByStudentAndUuid(User studentUser, String uuid);

  List<ScheduleTestStudent> findByStatusAndResultProcessingTimeBefore(
      String status, LocalDateTime resultProcessingTime);

  @Query(
      value =
          """
      select * from test_schedule_student where allowed_end_time is not null and
      status in ('STARTED','PENDING') and allowed_end_time < now()""",
      nativeQuery = true)
  List<ScheduleTestStudent> findTestsToBeAutoSubmitted();

  @Query(
      value =
          """
                  select * from test_schedule_student tss where tss.schedule_test_id in (:scheduleTestIds)
           """,
      nativeQuery = true)
  List<ScheduleTestStudent> getAllStudentsByScheduledIds(List<Long> scheduleTestIds);

  @Query(
      value =
          "select * from  test_schedule_student tss  where schedule_test_id = :scheduleTestId and uuid like concat(:uuidPrefix,'%')",
      nativeQuery = true)
  Optional<ScheduleTestStudent> fetchByScheduleTestAndPrefixUuid(
      long scheduleTestId, String uuidPrefix);

  Optional<ScheduleTestStudent> findByScheduleTestAndStudent(
      ScheduleTest scheduleTest, User student);

  Long countByScheduleTestAndStatusIn(ScheduleTest scheduleTest, List<String> status);

  @Query(
      value =
          """
                        select tss.*  from test_schedule_student tss
                        join test_schedule ts  on ts.id = tss.schedule_test_id
                        join test_definitions td on td.id = ts.test_definition_id
                        where td.id = :testDefId and tss.student_id = :studentId""",
      nativeQuery = true)
  List<ScheduleTestStudent> findByTestDefinitionAndStudentId(long studentId, long testDefId);

  @Query(
      value =
          """
                  select testname as testName, sum(aa.completedStudents) as completedStudents, sum(aa.pendingStudents)as pendingStudents, sum(aa.totalStudents) as totalStudents\s from
                  (select  a.testName,a.completedStudents,a.pendingStudents,a.totalStudents
                    from users u
                    join teacher_details td on td.user_id = u.id
                    join test_definitions tdf on tdf.teacher_id = u.id
                    join test_schedule ts on ts.test_definition_id = tdf.id
                    join orgs o on o.slug = ts.org_slug
                    join teacher_orgs tot on tot.teacher_id = td.id and tot.child_org = o.id
                    join ( select td.test_name as testName, td.id test_definition_id, ts.id test_schedule_id,
                    	sum(case when tss.status = 'COMPLETED' then 1 else 0 end) completedStudents,
                    	sum(case when tss.status = 'PENDING' then 1 else 0 end) pendingStudents,
                    	sum(case when tss.status not in ('COMPLETED', 'PENDING') then 1 else 0 end) others,
                    	count(tss.id) totalStudents
                    	from test_definitions td
                    	join test_schedule ts on ts.test_definition_id = td.id
                    	join orgs o on o.slug = ts.org_slug
                    	join test_schedule_student tss on tss.schedule_test_id = ts.id and ts.org_slug = o.slug
                    	where td.deleted_at is null and date(ts.start_date) between :fromDate and :toDate
                    	group by td.id, ts.id
                    	) a on a.test_definition_id = tdf.id and a.test_schedule_id = ts.id
                    where u.auth_user_id = :authUserId and u.organization = :orgSlug and date(ts.start_date) between :fromDate and :toDate) aa
                    group by testName
                  """,
      nativeQuery = true)
  List<ManagerScheduleOrgReport> getTeacherTestScheduleSummary(
      String authUserId, String orgSlug, LocalDate fromDate, LocalDate toDate);
}
