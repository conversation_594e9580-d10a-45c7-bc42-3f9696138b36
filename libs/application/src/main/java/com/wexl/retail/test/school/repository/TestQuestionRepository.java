package com.wexl.retail.test.school.repository;

import com.wexl.retail.test.school.domain.TestDefinitionSection;
import com.wexl.retail.test.school.domain.TestQuestion;
import com.wexl.retail.test.school.dto.TestSectionResponse;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

@Repository
public interface TestQuestionRepository extends JpaRepository<TestQuestion, Long> {
  void deleteByTestDefinitionSection(TestDefinitionSection testDefinitionSection);

  TestQuestion findByTestDefinitionSectionAndQuestionUuid(
      TestDefinitionSection testDefinitionSection, String uuid);

  List<TestQuestion> findByTestDefinitionSectionOrderById(
      TestDefinitionSection testDefinitionSection);

  long countByTestDefinitionSection(TestDefinitionSection testDefinitionSection);

  TestQuestion findByIdAndQuestionUuid(long id, String uuid);

  TestQuestion findByTestDefinitionSectionAndQuestionUuidAndId(
      TestDefinitionSection testDefinitionSection, String uuid, Long questionId);

  @Query(
      value =
          """
                  SELECT tds.id as sectionId, tds.name as sectionName,
                         q.chapter_name as chapterName,
                         q.type,
                         q.chapter_slug as chapterSlug,
                         SUM(q.marks) as marks,
                         COUNT(q.id) as questionCount,
                         q.category as category,
                         q.complexity as complexity
                         from test_questions q join test_definition_sections tds
                         on q.test_definition_section_id = tds.id
                         where q.test_definition_section_id in (:testDefinitionSectionIds)
                         GROUP BY tds.id, tds.name, q.chapter_slug, q.chapter_name, q.category , q.complexity,q.type
                  """,
      nativeQuery = true)
  List<TestSectionResponse> findSectionResponsesByTestDefinitionSectionIds(
      List<Long> testDefinitionSectionIds);

  @Transactional
  @Modifying
  @Query(
      value =
          """
                       delete from test_questions tq where test_definition_section_id = :sectionId
                       and question_uuid = :uuid
                          """,
      nativeQuery = true)
  void deleteQuestionBySectionAndUuid(String uuid, Long sectionId);

  List<TestQuestion> findAllByQuestionUuidAndPbqAnswersIsNotNull(String questionUuid);

  List<TestQuestion> findAllByTestDefinitionSectionInAndType(
      List<TestDefinitionSection> testDefinitionSections, String type);

  List<TestQuestion> findByTestDefinitionSection(TestDefinitionSection testDefinitionSection);
}
